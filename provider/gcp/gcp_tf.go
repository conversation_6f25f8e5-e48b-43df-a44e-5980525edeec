package gcp

import (
	"bytes"
	"encoding/json"
	"path/filepath"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

type GCPResourcesRequest struct {
	Parent string `json:"parent"`
	Query  string `json:"query"`
}

type GCPResourcesResponse struct {
	Labels map[string]string `json:"labels"`
}

func ProcessGCPTerraformEvents(tenantID, envID, projectID string, eventStartTime, eventEndTime, tenantStartTime time.Time) {

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.GCP_TF_RESOURCE, tenantStartTime)

	if (eventEndTime.Sub(eventStartTime)) > eventEndTime.Sub(defaultTime) {
		eventStartTime = defaultTime
	}

	startTime := elastic.DateTime(eventStartTime)
	endTime := elastic.DateTime(eventEndTime)

	searchQuery := `{"query":{"bool":{"filter":[{"match":{"sourceApp.keyword":"Terraform"}},{"range":{"eventTime":{"gt":"` + startTime + `","lte":"` + endTime + `"}}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"accountId.keyword":"` + projectID + `"}}]}}}`

	tfEventDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_ACTIVITY_INDEX}, searchQuery)
	if err != nil {
		return
	}

	if len(tfEventDocs) > 0 {
		logger.Print(logger.INFO, "Fetched gcp terraform events from "+startTime+" to "+endTime, []string{tenantID, projectID})
	}

	resourceIdToEventDocMap := make(map[string]common.ResourceEventCopy)
	resourceTypeToIDsMap := make(map[string][]string)
	uniqueResourceIds := make(map[string]struct{})
	lastEventTime := eventStartTime

	var terraformResourceDocs []common.TerraformResourceDoc

	for _, tfEventDoc := range tfEventDocs {

		if resourceInterfaces, ok := tfEventDoc["resources"].([]any); ok {
			for _, resourceInterface := range resourceInterfaces {

				if resourceMap, ok := resourceInterface.(map[string]any); ok {

					if resourceType, ok := resourceMap["resourceType"].(string); ok {

						resourceIDs := make([]string, 0)

						if resourceID, ok := resourceMap["resourceName"].(string); ok {

							if unmodifiedRscID, ok := resourceMap["unmodifiedResourceName"].(string); ok && len(unmodifiedRscID) > 0 {
								resourceID = unmodifiedRscID
							}

							if strings.Contains(resourceID, "[") && strings.Contains(resourceID, "]") {

								trimmedID := strings.Trim(resourceID, "[]")
								trimmedIDs := strings.Split(trimmedID, ",")

								resourceIDs = append(resourceIDs, trimmedIDs...)

							} else {
								resourceIDs = append(resourceIDs, resourceID)
							}
						}

						for _, resourceID := range resourceIDs {

							if cloudTrailEvent, ok := tfEventDoc["cloudTrailEvent"].(string); ok {

								if eventName, ok := tfEventDoc["eventName"].(string); ok {

									if serviceCode, ok := tfEventDoc["serviceCode"].(string); ok {

										if eventTimeStr, ok := tfEventDoc["eventTime"].(string); ok {

											if accountID, ok := tfEventDoc["accountId"].(string); ok {

												if region, ok := tfEventDoc["region"].(string); ok {

													eventTime, err := elastic.ParseDateTime(eventTimeStr)
													if err != nil {
														logger.Print(logger.ERROR, "Error parse date time", eventTime)
														return
													}
													if eventTime.After(lastEventTime) {
														lastEventTime = eventTime
													}

													if resourceType == "" || resourceID == "" {
														continue
													}

													eventResourceId := resourceID

													if strings.Contains(strings.ToLower(eventName), "delete") {
														//TODO: Fetch From DB
														continue
													} else {

														var cloudTrailEventMap map[string]any

														err = json.Unmarshal([]byte(cloudTrailEvent), &cloudTrailEventMap)
														if err != nil {
															logger.Print(logger.ERROR, "Error unmarshalling JSON", cloudTrailEvent, err)
															return
														}

														normalizeResourceForGCPSDC(&resourceID, resourceType, accountID, region)

														if _, ok := uniqueResourceIds[resourceID]; !ok {
															uniqueResourceIds[resourceID] = struct{}{}
															if resourceIDs, ok := resourceTypeToIDsMap[resourceType]; !ok {
																ids := make([]string, 0)
																ids = append(ids, resourceID)
																resourceTypeToIDsMap[resourceType] = ids

															} else {
																resourceIDs = append(resourceIDs, resourceID)
																resourceTypeToIDsMap[resourceType] = resourceIDs
															}

															resourceIdToEventDocMap[resourceID] = common.ResourceEventCopy{
																ResourceMap:     cloudTrailEventMap,
																ResourceType:    resourceType,
																ResourceName:    resourceID,
																ServiceCode:     serviceCode,
																EventName:       eventName,
																EventTime:       eventTime,
																Region:          region,
																EventResourceID: eventResourceId,
															}
														}
													}

													var precizeTfTagged bool

													var terraformResourceDoc = common.TerraformResourceDoc{
														Account: projectID,
														CSP:     common.GCP_SERVICE_CODE,
													}

													var (
														buf               bytes.Buffer
														gcpResourcesResp  GCPResourcesResponse
														gcpResourcesUrl   = `/precize/private/gcp/getResourcesByFilter/` + envID
														gcpResourceParent = "projects/" + projectID
														gcpResourcesQuery = "name:" + filepath.Base(resourceID)

														gcpResourcesRequest = GCPResourcesRequest{
															Parent: gcpResourceParent,
															Query:  gcpResourcesQuery,
														}
													)

													err = json.NewEncoder(&buf).Encode(gcpResourcesRequest)
													if err != nil {
														logger.Print(logger.ERROR, "Got error encoding request body", []string{tenantID, projectID}, err)
														continue
													}

													getGCPResources, err := transport.SendRequestToServer("POST", gcpResourcesUrl, nil, &buf)
													if err != nil {
														continue
													}

													if len(getGCPResources) > 0 {

														if err = json.Unmarshal(getGCPResources, &gcpResourcesResp); err != nil {
															logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID, projectID}, err)
															continue
														}

														for key, value := range gcpResourcesResp.Labels {

															switch key {
															case "precize_git_commit":
																precizeTfTagged = true
																terraformResourceDoc.CommitID = value
															case "precize_git_repo":
																terraformResourceDoc.Repository = value
															case "precize_git_file":
																terraformResourceDoc.Filename = value
															}
														}

														if !precizeTfTagged {
															continue
														}

														terraformResourceDoc.EventTime = eventTimeStr

														terraformResourceDoc.Approach = "yor"

														eventTime, err := time.Parse(elastic.DATE_FORMAT, eventTimeStr)
														if err != nil {
															logger.Print(logger.ERROR, "Got error parsing event time", []string{tenantID, projectID}, err)
															// return
														}

														if eventTime.After(lastEventTime) {
															lastEventTime = eventTime
														}

														terraformResourceDocs = append(terraformResourceDocs, terraformResourceDoc)
													}
												}
											}

										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	lastEventTimeTf, err := common.MapEventToTfResources(resourceIdToEventDocMap, resourceTypeToIDsMap, common.GCP_SERVICE_CODE, tenantID, projectID, eventStartTime)
	if err != nil {
		return
	}

	if !lastEventTimeTf.IsZero() && lastEventTimeTf.After(eventEndTime) {
		lastEventTime = lastEventTimeTf
	}

	for _, terraformResourceDoc := range terraformResourceDocs {

		if _, err = elastic.InsertDocument(tenantID, elastic.TERRAFORM_RESOURCES_INDEX, terraformResourceDoc); err != nil {
			continue
		}

		gitUsers, err := common.FetchGitFileAuthorsForTerraform(tenantID, terraformResourceDoc.CommitID,
			terraformResourceDoc.Filename, terraformResourceDoc.ResourceID, common.GCP_SERVICE_CODE)
		if err != nil {
			logger.Print(logger.ERROR, "Got error fetching git file authors", []string{tenantID, terraformResourceDoc.Account}, err)
			continue
		}

		for _, gitUser := range gitUsers {

			resourceEvent := common.ResourceEvent{
				ResourceID:   terraformResourceDoc.ResourceID,
				ResourceName: terraformResourceDoc.ResourceID,
				Region:       terraformResourceDoc.Region,
				Account:      terraformResourceDoc.Account,
			}

			if _, err = elastic.InsertDocument(tenantID, elastic.RESOURCE_USER_EVENTS_INDEX, common.ResourceUserEventDoc{
				ResourceEvent: resourceEvent,
				Action:        gitUser.Action,
				TenantID:      tenantID,
				User:          gitUser.Name,
				UserType:      gitUser.Client,
				EventTime:     gitUser.CommitTime,
				DocID:         gitUser.DocID,
			}); err != nil {
				continue
			}
		}
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.GCP_TF_RESOURCE, lastEventTime)
}

func normalizeResourceForGCPSDC(resourceID *string, resourceType, accountID, region string) {

	// for few cases irrespective of project, localtion, region we want to build the resource id
	if len(region) > 0 && strings.ToLower(region) != "na" && len(accountID) != 0 {
		baseRscID := filepath.Base(*resourceID)
		switch resourceType {
		case common.GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE, common.GCP_INSTANCEGROUP_RESOURCE_TYPE, common.GCP_RESOURCEPOLICY_RESOURCE_TYPE, common.GCP_GKECLUSTER_RESOURCE_TYPE:
			*resourceID = "projects/" + accountID + "/zones/" + region + "/" + baseRscID

			return
		}
	}

	if !strings.Contains(*resourceID, "/zones/") && !strings.Contains(*resourceID, "/locations/") && !strings.Contains(*resourceID, "/regions/") && !strings.Contains(*resourceID, "/global/") {

		if len(region) > 0 && strings.ToLower(region) != "na" {

			switch resourceType {
			case common.GCP_KMS_RESOURCE_TYPE, common.GCP_IMAGE_RESOURCE_TYPE, common.GCP_NETWORK_RESOURCE_TYPE, common.GCP_SQLDB_RESOURCE_TYPE, common.GCP_ARTIFACTREPOSITORY_RESOURCE_TYPE, common.GCP_PUBSUBSUBSCRIPTION_RESOURCE_TYPE, common.GCP_SSLPOLICY_RESOURCE_TYPE, common.GCP_DNSMANAGEDZONE_RESOURCE_TYPE, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, common.GCP_FIREWALL_RESOURCE_TYPE, common.GCP_DISK_SNAPSHOT_RESOURCE_TYPE, common.GCP_BIGQUERYDATASET_RESOURCE_TYPE, common.GCP_PROJECT_RESOURCE_TYPE, common.GCP_USER_RESOURCE_TYPE, common.GCP_FOLDER_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE, common.GCP_ROLE_RESOURCE_TYPE, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, common.GCP_GROUP_RESOURCE_TYPE, common.GCP_PUBSUBTOPIC_RESOURCE_TYPE:
			// region not be added for these
			case common.GCP_BACKENDSERVICE_RESOURCE_TYPE, common.GCP_BIGQUERYTRANSFERCONFIG_RESOURCE_TYPE, common.LOADBALANCER_RESOURCE_TYPE, common.GCP_SUBNETWORK_RESOURCE_TYPE, common.GCP_LBTARGETHTTPSPROXY_RESOURCE_TYPE:
				*resourceID = "regions/" + region + "/" + *resourceID
			case common.GCP_DATAFORMREPOSITORY_RESOURCE_TYPE, common.GCP_FUNCTION_RESOURCE_TYPE, common.GCP_CLOUDRUNSERVICE_RESOURCE_TYPE, common.GCP_VECTORSEARCHINDEXES_RESOURCE_TYPE, common.GCP_VECTORSEARCHINDEXENDPOINT_RESOURCE_TYPE, common.GCP_VERTEXAIPROMPT_RESOURCE_TYPE:
				*resourceID = "locations/" + region + "/" + *resourceID
			case common.GCP_GKECLUSTER_RESOURCE_TYPE, common.GCP_AUTOSCALER_RESOURCE_TYPE, common.GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE, common.GCP_INSTANCEGROUP_RESOURCE_TYPE, common.GCP_VMDISK_RESOURCE_TYPE, common.GCP_INSTANCE_RESOURCE_TYPE, common.GCP_DOCUMENTAIPROCESSOR_RESOURCE_TYPE, common.GCP_VERTEXAIENDPOINT_RESOURCE_TYPE, common.GCP_VERTEXAIFEATUREONLINESTORE_RESOURCE_TYPE, common.GCP_VERTEXAIFEATUREVIEW_RESOURCE_TYPE, common.GCP_VERTEXAIMETADATASTORE_RESOURCE_TYPE, common.GCP_VERTEXAIMODEL_RESOURCE_TYPE, common.GCP_VERTEXAINOTEBOOKINSTANCE_RESOURCE_TYPE, common.GCP_VERTEXAITRAININGPIPELINE_RESOURCE_TYPE, common.GCP_BIGQUERYTABLE_RESOURCE_TYPE:
				*resourceID = "zones/" + region + "/" + *resourceID
			default:
				*resourceID = "zones/" + region + "/" + *resourceID
			}
		}
	}

	if !strings.Contains(*resourceID, "projects/") || (strings.Contains(*resourceID, "projects/_/")) {

		if len(accountID) == 0 {
			return
		}

		switch resourceType {
		case common.GCP_PROJECT_RESOURCE_TYPE, common.GCP_USER_RESOURCE_TYPE, common.GCP_FOLDER_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE, common.GCP_ROLE_RESOURCE_TYPE, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, common.GCP_GROUP_RESOURCE_TYPE:
		default:
			*resourceID = "projects/" + accountID + "/" + *resourceID
		}
	}
}
