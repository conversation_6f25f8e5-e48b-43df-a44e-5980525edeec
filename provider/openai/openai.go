package openai

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/gocarina/gocsv"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/email"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

const (
	baseURL               = "https://api.openai.com/v1"
	bufferTime            = 24 * time.Hour
	vectorStoreBufferTime = 48 * time.Hour
	eventMax              = 48 * time.Hour
)

var serviceAccountAbsentEmailSent map[string]bool

func init() {
	serviceAccountAbsentEmailSent = make(map[string]bool)
}

func ValidateAuth(openAIEnv tenant.OpenAIEnvironment, tenantID string) (authValidation map[string]bool, err error) {

	authValidation = make(map[string]bool)
	projectIDPattern := regexp.MustCompile(`^proj_[a-zA-Z0-9]{24}$`)

	for _, adminKey := range openAIEnv.AdminTokens {

		headers := map[string]string{"Authorization": "Bearer " + adminKey}
		queryParams := map[string]string{
			"limit": "1",
		}

		if _, err1 := sendOpenAIOrgRequest("GET", baseURL+"/organization/users", queryParams, headers, nil); err1 != nil {
			err = err1
			authValidation[adminKey] = false
		} else {
			authValidation[adminKey] = true
		}
	}

	for _, proj := range openAIEnv.Projects {

		if !projectIDPattern.MatchString(proj.ProjectID) {
			authValidation[proj.ServiceAccountKey] = false
			err = fmt.Errorf("Invalid project ID %s:", proj.ProjectID)
			continue
		}

		headers := map[string]string{"Authorization": "Bearer " + proj.ServiceAccountKey}
		queryParams := map[string]string{
			"limit": "1",
		}

		if _, err2 := sendOpenAIProjectRequest("GET", baseURL+"/batches", queryParams, headers, nil, proj.ProjectID, tenantID, false); err2 != nil {
			err = err2
			authValidation[proj.ServiceAccountKey] = false
		} else {
			authValidation[proj.ServiceAccountKey] = true
		}
	}

	return
}

func ProcessOpenAIData(tenantID string, openAIEnv tenant.OpenAIEnvironment, openAIStartTime, openAIEndTime time.Time) {

	var (
		projects         = make(map[string]string)
		collectedAt      = time.Now().Unix()
		defaultProjectID = ""
		globalAdminKey   = ""
		globalOrgID      = ""
	)

	// Start time only used for checking if bufferTime passed. OpenAI always collects from beginning of time
	if openAIEndTime.Sub(openAIStartTime) < bufferTime {
		return
	}

	logger.Print(logger.INFO, "Fetching openai resources from "+elastic.DateTime(openAIStartTime)+" until "+elastic.DateTime(openAIEndTime), []string{tenantID})

	for _, v := range openAIEnv.Projects {
		saKey := v.ServiceAccountKey
		if len(v.ProjectID) <= 0 {
			v.ProjectID = "Unknown-project-" + saKey[len(saKey)-5:]
		}
		projects[v.ProjectID] = saKey
	}

	for _, adminKey := range openAIEnv.AdminTokens {

		orgID := ORGANIZATION_NAME + "-Precize" + openAIEnv.ID

		globalAdminKey = adminKey
		globalOrgID = orgID

		collectOrganization(tenantID, orgID, collectedAt)
		collectAuditLogs(tenantID, adminKey, orgID, openAIStartTime, openAIEndTime)
		collectUsers(tenantID, adminKey, orgID, collectedAt)
		collectAdminAPIKeys(tenantID, adminKey, orgID, collectedAt)
		projectKeys := collectProjectMetaDataAndFetchKeys(tenantID, adminKey, orgID, collectedAt, &defaultProjectID)

		if len(projectKeys) > 0 {
			// If one admin key works
			projects = projectKeys
			break
		}
	}

	time.Sleep(10 * time.Second)

	var aiMetaData = OpenAIMetaData{
		TenantID:            tenantID,
		CollectedAt:         collectedAt,
		StaticModelMetaData: staticModelMetaData,
		DefaultProjectID:    defaultProjectID,
		startTime:           openAIStartTime,
		endTime:             openAIEndTime,
		AdminKey:            globalAdminKey,
		OrgID:               globalOrgID,
	}

	aiMetaData.AIMetaDataMap = AIMetaData{
		ModelMetaData:         make(map[string]ModelMetaData),
		FineTuningJobMetaData: make(map[string]FineTuningJobMetaData),
		FileMetaData:          make(map[string]FileMetaData),
	}

	for projectID, saKey := range projects {
		logger.Print(logger.INFO, "Processing Project", []string{tenantID}, projectID)

		aiMetaData.SaKey = saKey
		aiMetaData.ProjectID = projectID

		collectBatches(&aiMetaData)
		collectAssistants(&aiMetaData)
		collectFineTuningData(&aiMetaData)
		collectFiles(&aiMetaData)
		collectProjectRateLimits(&aiMetaData)

		// project creation only if project onb, org onb will automatically create all projects
		insertProjectRecordForProjectOnb(projectID, tenantID, aiMetaData.CollectedAt)
	}

	time.Sleep(10 * time.Second)

	var (
		modelsMissingMetadata      = make([]string, 0)
		modelsMissingBlockedStatus = make([]string, 0)
	)

	// Default project models are enabled in other projects by default
	for projectID, saKey := range projects {
		logger.Print(logger.INFO, "Processing Models", []string{tenantID}, projectID)

		aiMetaData.SaKey = saKey
		aiMetaData.ProjectID = projectID

		collectModels(&aiMetaData, &modelsMissingMetadata, &modelsMissingBlockedStatus)
	}

	if len(modelsMissingMetadata) > 0 || len(modelsMissingBlockedStatus) > 0 {
		subject := "Model Data missing for OpenAI Models in " + config.Environment
		body := "Model MetaData is missing for models: " + strings.Join(modelsMissingMetadata, ",") + " for tenant " + aiMetaData.TenantID + " for project " + aiMetaData.ProjectID
		body += "\n\nModel Blocked Status is missing for models: " + strings.Join(modelsMissingBlockedStatus, ",") + " for tenant " + aiMetaData.TenantID + " for project " + aiMetaData.ProjectID

		recipients := map[string]string{
			"Aniket": "<EMAIL>",
			"Mathan": "<EMAIL>",
		}
		cc := map[string]string{}
		if config.Environment == config.PROD_ENV {
			cc["Abhay"] = "<EMAIL>"
		}

		email.SendEmail(subject, body, recipients, cc, nil)
	}

	if len(aiMetaData.AdminKey) > 0 {
		// Only if org access is enabled

		collectUsage(&aiMetaData, openAIStartTime, openAIEndTime)
		calculateModelCost(&aiMetaData)
	}

	for projectID, saKey := range projects {
		logger.Print(logger.INFO, "Processing Vector Store", []string{tenantID}, projectID)

		aiMetaData.SaKey = saKey
		aiMetaData.ProjectID = projectID
		if err := collectVectorStore(&aiMetaData); err == nil {
			// since vector store and vector store files are interdependent, if vector store collection fails, no need to collect vector store files
			collectVectorStoreFiles(&aiMetaData)
		}
	}

	time.Sleep(10 * time.Second)

	// delete older docs
	deleteTrueQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}}],"must_not":[{"match":{"collectedAt":"` + strconv.Itoa(int(collectedAt)) + `"}}]}},"script":"ctx._source.deleted = true;"}`
	if err := elastic.UpdateByQuery(elastic.AI_RESOURCES_INDEX, deleteTrueQuery); err != nil {
		logger.Print(logger.ERROR, "Got error deleting older openAI docs", []string{tenantID}, err)
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.OPENAI_RESOURCES, openAIEndTime)

	return
}

func insertOpenAIResourceToDB(tenantID, entityType, accountID string, openAIDocs []common.AIResourcesDoc) error {

	var (
		bulkOpenAIRequest string
		docCount          int
		maxRecords        = 1000
	)

	for _, openAIDoc := range openAIDocs {

		openAIInsertMetadata := `{"index": {"_id": "` + openAIDoc.ID + `"}}`
		openAIInsertDoc, err := json.Marshal(openAIDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling document", err)
			return err
		}

		bulkOpenAIRequest = bulkOpenAIRequest + openAIInsertMetadata + "\n" + string(openAIInsertDoc) + "\n"

		docCount++

		if docCount >= maxRecords {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.AI_RESOURCES_INDEX, bulkOpenAIRequest); err != nil {
				return err
			}

			logger.Print(logger.INFO, "OpenAI bulk API Successful for "+strconv.Itoa(docCount)+" "+entityType+" records", []string{tenantID})

			docCount = 0
			bulkOpenAIRequest = ""
		}
	}

	if len(bulkOpenAIRequest) > 0 {

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.AI_RESOURCES_INDEX, bulkOpenAIRequest); err != nil {
			return err
		}

		logger.Print(logger.INFO, "OpenAI bulk API Successful for "+strconv.Itoa(docCount)+" "+entityType+" records", []string{tenantID, accountID})
	}

	return nil
}

func sendOpenAIOrgRequest(method, url string, urlParams, headers map[string]string, reqBody io.Reader) (resp []byte, err error) {
	return transport.SendRequest(method, url, urlParams, headers, reqBody)
}

func sendOpenAIProjectRequest(method, url string, urlParams, headers map[string]string, reqBody io.Reader, projectID, tenantID string, emailNotif bool) (resp []byte, err error) {
	const maxRetries = 3
	baseDelay := time.Second

	for attempt := 0; attempt <= maxRetries; attempt++ {
		resp, err = transport.SendRequest(method, url, urlParams, headers, reqBody, transport.WithHandledErrorCodes(http.StatusUnauthorized,
			http.StatusInternalServerError,
			http.StatusBadGateway,
			http.StatusServiceUnavailable))
		if err == nil {
			return resp, nil
		}

		if strings.Contains(err.Error(), "Status code: "+strconv.Itoa(http.StatusUnauthorized)) {
			if !serviceAccountAbsentEmailSent[tenantID] && emailNotif {
				subject := "Service account absent in Customer's OpenAI"
				body := "Precize created service account is missing for tenant " + tenantID + " for project " + projectID

				recipients := map[string]string{
					"Aniket": "<EMAIL>",
					"Abhay":  "<EMAIL>",
				}
				cc := map[string]string{}
				if config.Environment != config.QA_ENV && config.Environment != config.PREPROD_ENV {
					cc["Sowmya"] = "<EMAIL>"
				}

				email.SendEmail(subject, body, recipients, cc, nil)
				serviceAccountAbsentEmailSent[tenantID] = true
			}

			return nil, err
		}

		// Retry only for transient server errors
		if strings.Contains(err.Error(), "Status code: "+strconv.Itoa(http.StatusInternalServerError)) ||
			strings.Contains(err.Error(), "Status code: "+strconv.Itoa(http.StatusBadGateway)) ||
			strings.Contains(err.Error(), "Status code: "+strconv.Itoa(http.StatusServiceUnavailable)) {

			if attempt < maxRetries {
				sleepDuration := baseDelay * time.Duration(math.Pow(2, float64(attempt)))
				logger.Print(logger.INFO, "Retrying OpenAI request in", sleepDuration, "seconds", []string{tenantID})
				time.Sleep(sleepDuration)
				continue
			}
		}

		// other error
		break
	}

	return nil, err
}

func collectOrganization(tenantID, orgID string, collectedAt int64) {

	//TODO: change this to Tier 1 once property is added in company metadata (prod)
	tier := "Tier 5"
	if orgTier := getOrganizationTierForTenant(tenantID); len(orgTier) > 0 {
		tier = orgTier
	}

	aiResourcesDoc := common.AIResourcesDoc{
		EntityID:       orgID,
		EntityType:     common.OPENAI_ORG_RESOURCE_TYPE,
		AccountID:      orgID,
		StageCompleted: "dc",
		Deleted:        false,
		CollectedAt:    collectedAt,
		ServiceID:      common.OPENAI_SERVICE_ID_INT,
		TenantID:       tenantID,
		EntityJson:     `{"name":"` + ORGANIZATION_NAME + `","tier":"` + tier + `"}`,
	}

	aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)

	if _, err := elastic.InsertDocument(tenantID, elastic.AI_RESOURCES_INDEX, aiResourcesDoc, aiResourcesDoc.ID); err != nil {
		return
	}

	return
}

func collectProjectMetaDataAndFetchKeys(tenantID, token, orgID string, collectedAt int64, defaultProjectID *string) map[string]string {

	var (
		after                 string
		headers               = map[string]string{"Authorization": "Bearer " + token}
		projectKeyMap         = make(map[string]string)
		projectAIResourceDocs []common.AIResourcesDoc
	)

	for {

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit":            "100",
			"include_archived": "true",
		}

		if len(after) > 0 {
			queryParams["after"] = after
		}

		resp, err := sendOpenAIOrgRequest("GET", baseURL+"/organization/projects", queryParams, headers, nil)
		if err != nil {
			return nil
		}

		var projectsResponse ProjectsResponse

		if err = json.Unmarshal(resp, &projectsResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			break
		}

		for _, project := range projectsResponse.Data {

			if project.Name == "Default project" {
				*defaultProjectID = project.ID
			}

			deleted := false
			if project.ArchivedAt > 0 {
				deleted = true
			}

			if !deleted {
				fetchOrCreateServiceAccount(tenantID, project.ID, token, projectKeyMap)
				collectProjectUsers(tenantID, project.ID, token, collectedAt)
				collectProjectServiceAccounts(tenantID, project.ID, token, collectedAt)
				collectProjectAPIKeys(tenantID, project.ID, token, collectedAt)
			} else {
				deleteProjectResourcesQuery := `{"query":{"bool":{"must":[{"match":{"accountId.keyword":"` + project.ID + `"}},{"match":{"tenantId.keyword":"` + tenantID + `"}}]}},"script":"ctx._source.deleted = true;"}`
				err := elastic.UpdateByQuery(elastic.AI_RESOURCES_INDEX, deleteProjectResourcesQuery)
				if err != nil {
					continue
				}
			}

			// by default model blocking is not enabled, during model collection if it is enabled, it will be updated in the project json
			project.IsModelBlockingEnabled = false
			entityJson, err := json.Marshal(project)
			if err != nil {
				continue
			}

			aiResourcesDoc := common.AIResourcesDoc{
				EntityID:       project.ID,
				EntityType:     common.OPENAI_PROJECT_RESOURCE_TYPE,
				AccountID:      orgID,
				StageCompleted: "dc",
				Deleted:        deleted,
				CollectedAt:    collectedAt,
				CreatedDate:    project.CreatedAt,
				ServiceID:      common.OPENAI_SERVICE_ID_INT,
				TenantID:       tenantID,
				EntityJson:     string(entityJson),
			}

			aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
			projectAIResourceDocs = append(projectAIResourceDocs, aiResourcesDoc)
		}

		if projectsResponse.HasMore {
			after = projectsResponse.LastID
		} else {
			break
		}
	}

	if err := insertOpenAIResourceToDB(tenantID, common.OPENAI_PROJECT_RESOURCE_TYPE, orgID, projectAIResourceDocs); err != nil {
		return projectKeyMap
	}

	return projectKeyMap
}

func fetchOrCreateServiceAccount(tenantID, projectID, token string, projectKeyMap map[string]string) {

	var (
		serviceAccountName     = PRECIZE_SERVICEACCOUNT_PREFIX + projectID
		serviceAccountKeyDocID = common.GenerateCombinedHashID(serviceAccountName, projectID, "ServiceAccountKey", common.OPENAI_SERVICE_ID, tenantID)
	)

	saKeyDoc, err := elastic.GetDocument(elastic.PRECIZE_CREATIONS_INDEX, serviceAccountKeyDocID)
	if err != nil {
		return
	}

	if len(saKeyDoc) > 0 {
		if saKey, ok := saKeyDoc["id"].(string); ok && len(saKey) > 0 {
			decodedSaKey, err := base64.StdEncoding.DecodeString(saKey)
			if err == nil {
				decryptedKey, err := common.DecryptTextAES(decodedSaKey)
				if err == nil {
					projectKeyMap[projectID] = string(decryptedKey)
				} else {
					projectKeyMap[projectID] = saKey
				}
			} else {
				logger.Print(logger.ERROR, "Failed to decode base64", err)
				projectKeyMap[projectID] = saKey
			}
		}

		return
	}

	// Additional check to make sure that precize service account does not exist, before we create it

	var (
		after         string
		listSAHeaders = map[string]string{"Authorization": "Bearer " + token}
	)

	for {

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit": "100",
		}

		if len(after) > 0 {
			queryParams["after"] = after
		}

		resp, err := sendOpenAIOrgRequest("GET", baseURL+"/organization/projects/"+projectID+"/service_accounts", queryParams, listSAHeaders, nil)
		if err != nil {
			return
		}

		var projectServiceAccountsResponse ProjectServiceAccountsResponse

		if err = json.Unmarshal(resp, &projectServiceAccountsResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			return
		}

		for _, projectServiceAccount := range projectServiceAccountsResponse.Data {
			if projectServiceAccount.Name == PRECIZE_SERVICEACCOUNT_PREFIX+projectID {
				err := errors.New("Precize Service Account exists but DB did not return")
				logger.Print(logger.ERROR, "Service Account already exists", []string{tenantID, projectID}, err, projectServiceAccount)
				return
			}
		}

		if projectServiceAccountsResponse.HasMore {
			after = projectServiceAccountsResponse.LastID
		} else {
			break
		}
	}

	// Precize Service Account does not exist for this project, Creating it

	var (
		createSABuff                bytes.Buffer
		headers                     = map[string]string{"Authorization": "Bearer " + token, "Content-Type": "application/json"}
		createServiceAccountRequest = CreateServiceAccountRequest{
			Name: serviceAccountName,
		}
		createServiceAccountResponse CreateServiceAccountResponse
	)

	if err = json.NewEncoder(&createSABuff).Encode(createServiceAccountRequest); err != nil {
		logger.Print(logger.ERROR, "Got error encoding request body", []string{tenantID}, err)
		return
	}

	resp, err := sendOpenAIOrgRequest("POST", baseURL+"/organization/projects/"+projectID+"/service_accounts", nil, headers, &createSABuff)
	if err != nil {
		return
	}

	// Giving time for creating to make sure the service account we create is collected later.
	time.Sleep(2 * time.Second)

	if err = json.Unmarshal(resp, &createServiceAccountResponse); err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
		return
	}

	saKey := createServiceAccountResponse.APIKey.Value

	if len(saKey) > 0 {

		projectKeyMap[projectID] = saKey

		encryptedKey, err := common.EncryptTextAES([]byte(createServiceAccountResponse.APIKey.Value))
		if err == nil {
			saKey = base64.StdEncoding.EncodeToString(encryptedKey)
		}

		if _, err = elastic.InsertDocument(tenantID, elastic.PRECIZE_CREATIONS_INDEX, common.PrecizeCreationsDoc{
			ID:          saKey,
			Name:        serviceAccountName,
			Type:        "ServiceAccountKey",
			Account:     projectID,
			Service:     "OpenAI",
			CreatedDate: elastic.DateTime(time.Now()),
			ServiceID:   common.OPENAI_SERVICE_ID_INT,
			TenantID:    tenantID,
		}, serviceAccountKeyDocID); err != nil {
			return
		}
	}

	return
}

func collectProjectUsers(tenantID, projectID, token string, collectedAt int64) {

	var (
		after                      string
		headers                    = map[string]string{"Authorization": "Bearer " + token}
		projectUsersAIResourceDocs []common.AIResourcesDoc
	)

	for {

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit": "100",
		}

		if len(after) > 0 {
			queryParams["after"] = after
		}

		resp, err := sendOpenAIOrgRequest("GET", baseURL+"/organization/projects/"+projectID+"/users", queryParams, headers, nil)
		if err != nil {
			return
		}

		var (
			projectUsersResponse ProjectUsersResponse
			usersList            []string
			userToEventMap       = make(map[string]common.Event)
		)

		if err = json.Unmarshal(resp, &projectUsersResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			break
		}

		for _, user := range projectUsersResponse.Data {
			usersList = append(usersList, user.Email)
		}

		eventsQuery := `{"query":{"bool":{"must":[{"match":{"eventSource.keyword":"openai_audit_log"}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"accountId.keyword":"` + projectID + `"}},{"terms":{"username.keyword":["` + strings.Join(usersList, `","`) + `"]}}]}},"aggs":{"by_username":{"terms":{"field":"username.keyword","size":10000},"aggs":{"latest_event":{"top_hits":{"size":1,"sort":[{"eventTime":{"order":"desc"}}]}}}}},"size":0}`

		eventsAggregation, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_ACTIVITY_INDEX}, eventsQuery)
		if err != nil {
			return
		}

		eventsAggregationBytes, err := json.Marshal(eventsAggregation)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling raw aggregation", []string{tenantID}, err)
			return
		}

		var parsedResp EventsAggregationResponse
		if err := json.Unmarshal(eventsAggregationBytes, &parsedResp); err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling events aggregation response", []string{tenantID}, err)
			return
		}

		for _, bucket := range parsedResp.ByUsername.Buckets {
			if len(bucket.LatestEvent.Hits.Hits) == 0 {
				continue
			}

			eventDoc := bucket.LatestEvent.Hits.Hits[0].Source
			rctxEvent := common.Event{
				Name:   eventDoc.EventName,
				Region: "NA",
				Time:   eventDoc.EventTime,
			}

			userToEventMap[bucket.Key] = rctxEvent
		}

		for _, projectUser := range projectUsersResponse.Data {

			entityJson, err := json.Marshal(projectUser)
			if err != nil {
				continue
			}

			aiResourcesDoc := common.AIResourcesDoc{
				EntityID:       projectUser.ID,
				EntityType:     common.OPENAI_USER_RESOURCE_TYPE,
				AccountID:      projectID,
				StageCompleted: "dc",
				Deleted:        false,
				CollectedAt:    collectedAt,
				CreatedDate:    projectUser.AddedAt,
				ServiceID:      common.OPENAI_SERVICE_ID_INT,
				TenantID:       tenantID,
				EntityJson:     string(entityJson),
			}

			if rctxEvent, ok := userToEventMap[projectUser.Email]; ok {
				eventTime, err := elastic.ParseDateTime(rctxEvent.Time)
				if err != nil {
					continue
				}

				aiResourcesDoc.LastWriteTime = eventTime.UnixMilli()
				aiResourcesDoc.LastWriteActivity = &rctxEvent
			}

			aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
			projectUsersAIResourceDocs = append(projectUsersAIResourceDocs, aiResourcesDoc)
		}

		if projectUsersResponse.HasMore {
			after = projectUsersResponse.LastID
		} else {
			break
		}
	}

	if err := insertOpenAIResourceToDB(tenantID, common.OPENAI_USER_RESOURCE_TYPE, projectID, projectUsersAIResourceDocs); err != nil {
		return
	}

	return
}

func collectProjectServiceAccounts(tenantID, projectID, token string, collectedAt int64) {

	var (
		after                                string
		headers                              = map[string]string{"Authorization": "Bearer " + token}
		projectServiceAccountsAIResourceDocs []common.AIResourcesDoc
	)

	for {

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit": "100",
		}

		if len(after) > 0 {
			queryParams["after"] = after
		}

		resp, err := sendOpenAIOrgRequest("GET", baseURL+"/organization/projects/"+projectID+"/service_accounts", queryParams, headers, nil)
		if err != nil {
			return
		}

		var projectServiceAccountsResponse ProjectServiceAccountsResponse

		if err = json.Unmarshal(resp, &projectServiceAccountsResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			break
		}

		for _, projectServiceAccount := range projectServiceAccountsResponse.Data {

			entityJson, err := json.Marshal(projectServiceAccount)
			if err != nil {
				continue
			}

			aiResourcesDoc := common.AIResourcesDoc{
				EntityID:       projectServiceAccount.ID,
				EntityType:     common.OPENAI_SERVICEACCOUNT_RESOURCE_TYPE,
				AccountID:      projectID,
				StageCompleted: "dc",
				Deleted:        false,
				CollectedAt:    collectedAt,
				CreatedDate:    projectServiceAccount.CreatedAt,
				ServiceID:      common.OPENAI_SERVICE_ID_INT,
				TenantID:       tenantID,
				EntityJson:     string(entityJson),
			}

			aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
			projectServiceAccountsAIResourceDocs = append(projectServiceAccountsAIResourceDocs, aiResourcesDoc)
		}

		if projectServiceAccountsResponse.HasMore {
			after = projectServiceAccountsResponse.LastID
		} else {
			break
		}
	}

	if err := insertOpenAIResourceToDB(tenantID, common.OPENAI_SERVICEACCOUNT_RESOURCE_TYPE, projectID, projectServiceAccountsAIResourceDocs); err != nil {
		return
	}

	return
}

func collectProjectAPIKeys(tenantID, projectID, token string, collectedAt int64) {

	var (
		after                        string
		headers                      = map[string]string{"Authorization": "Bearer " + token}
		projectAPIKeysAIResourceDocs []common.AIResourcesDoc
	)

	for {

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit": "100",
		}

		if len(after) > 0 {
			queryParams["after"] = after
		}

		resp, err := sendOpenAIOrgRequest("GET", baseURL+"/organization/projects/"+projectID+"/api_keys", queryParams, headers, nil)
		if err != nil {
			return
		}

		var projectAPIKeysResponse ProjectAPIKeysResponse

		if err = json.Unmarshal(resp, &projectAPIKeysResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			break
		}

		for _, projectAPIKey := range projectAPIKeysResponse.Data {

			entityJson, err := json.Marshal(projectAPIKey)
			if err != nil {
				continue
			}

			aiResourcesDoc := common.AIResourcesDoc{
				EntityID:       projectAPIKey.ID,
				EntityType:     common.OPENAI_APIKEY_RESOURCE_TYPE,
				AccountID:      projectID,
				StageCompleted: "dc",
				Deleted:        false,
				CollectedAt:    collectedAt,
				CreatedDate:    projectAPIKey.CreatedAt,
				ServiceID:      common.OPENAI_SERVICE_ID_INT,
				TenantID:       tenantID,
				EntityJson:     string(entityJson),
			}

			aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
			projectAPIKeysAIResourceDocs = append(projectAPIKeysAIResourceDocs, aiResourcesDoc)
		}

		if projectAPIKeysResponse.HasMore {
			after = projectAPIKeysResponse.LastID
		} else {
			break
		}
	}

	if err := insertOpenAIResourceToDB(tenantID, common.OPENAI_APIKEY_RESOURCE_TYPE, projectID, projectAPIKeysAIResourceDocs); err != nil {
		return
	}

	return
}

func collectAdminAPIKeys(tenantID, token, orgID string, collectedAt int64) {

	var (
		after                      string
		headers                    = map[string]string{"Authorization": "Bearer " + token}
		adminAPIKeysAIResourceDocs []common.AIResourcesDoc
	)

	for {

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit": "100",
		}

		if len(after) > 0 {
			queryParams["after"] = after
		}

		resp, err := sendOpenAIOrgRequest("GET", baseURL+"/organization/admin_api_keys", queryParams, headers, nil)
		if err != nil {
			return
		}

		var adminAPIKeysResponse AdminAPIKeysResponse

		if err = json.Unmarshal(resp, &adminAPIKeysResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			break
		}

		for _, adminAPIKey := range adminAPIKeysResponse.Data {

			if !strings.HasPrefix(adminAPIKey.RedactedValue, "sk-admin") {
				continue
			}

			entityJson, err := json.Marshal(adminAPIKey)
			if err != nil {
				continue
			}

			aiResourcesDoc := common.AIResourcesDoc{
				EntityID:       adminAPIKey.ID,
				EntityType:     common.OPENAI_ADMINAPIKEY_RESOURCE_TYPE,
				AccountID:      orgID,
				StageCompleted: "dc",
				Deleted:        false,
				CollectedAt:    collectedAt,
				CreatedDate:    adminAPIKey.CreatedAt,
				ServiceID:      common.OPENAI_SERVICE_ID_INT,
				TenantID:       tenantID,
				EntityJson:     string(entityJson),
			}

			aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
			adminAPIKeysAIResourceDocs = append(adminAPIKeysAIResourceDocs, aiResourcesDoc)
		}

		if adminAPIKeysResponse.HasMore {
			after = adminAPIKeysResponse.LastID
		} else {
			break
		}
	}

	if err := insertOpenAIResourceToDB(tenantID, common.OPENAI_ADMINAPIKEY_RESOURCE_TYPE, orgID, adminAPIKeysAIResourceDocs); err != nil {
		return
	}

	return
}

func collectProjectRateLimits(aiMetaData *OpenAIMetaData) {

	var (
		after   string
		headers = map[string]string{"Authorization": "Bearer " + aiMetaData.AdminKey}
	)

	for {

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit": "100",
		}

		if len(after) > 0 {
			queryParams["after"] = after
		}

		resp, err := sendOpenAIOrgRequest("GET", baseURL+"/organization/projects/"+aiMetaData.ProjectID+"/rate_limits", queryParams, headers, nil)
		if err != nil {
			return
		}

		var projectRateLimitResponse ProjectRateLimitResponse

		if err = json.Unmarshal(resp, &projectRateLimitResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{aiMetaData.TenantID}, err)
			break
		}

		for _, projectRateLimit := range projectRateLimitResponse.Data {

			modelMetaData := ModelMetaData{
				ModelID:                 projectRateLimit.Model,
				MaxRequestsPerMinute:    projectRateLimit.MaxRequestsPerMinute,
				MaxTokensPerMinute:      projectRateLimit.MaxTokensPerMinute,
				MaxImagesPerMinute:      projectRateLimit.MaxImagesPerMinute,
				MaxRequestsPerDay:       projectRateLimit.MaxRequestsPerDay,
				Batch1DayMaxInputTokens: projectRateLimit.Batch1DayMaxInputTokens,
				MaxAudioMegabytesPerMin: projectRateLimit.MaxAudioMegabytesPerMin,
			}

			if modelsMetaData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[projectRateLimit.Model+"+"+aiMetaData.ProjectID]; ok {

				modelsMetaData.MaxRequestsPerMinute = projectRateLimit.MaxRequestsPerMinute
				modelsMetaData.MaxTokensPerMinute = projectRateLimit.MaxTokensPerMinute
				modelsMetaData.MaxImagesPerMinute = projectRateLimit.MaxImagesPerMinute
				modelsMetaData.MaxRequestsPerDay = projectRateLimit.MaxRequestsPerDay
				modelsMetaData.Batch1DayMaxInputTokens = projectRateLimit.Batch1DayMaxInputTokens
				modelsMetaData.MaxAudioMegabytesPerMin = projectRateLimit.MaxAudioMegabytesPerMin

				aiMetaData.AIMetaDataMap.ModelMetaData[projectRateLimit.Model+"+"+aiMetaData.ProjectID] = modelsMetaData
			} else {
				aiMetaData.AIMetaDataMap.ModelMetaData[projectRateLimit.Model+"+"+aiMetaData.ProjectID] = modelMetaData
			}
		}

		if projectRateLimitResponse.HasMore {
			after = projectRateLimitResponse.LastID
		} else {
			break
		}
	}

	return
}

func collectUsers(tenantID, token, orgID string, collectedAt int64) {

	var (
		after               string
		headers             = map[string]string{"Authorization": "Bearer " + token}
		usersAIResourceDocs []common.AIResourcesDoc
	)

	for {

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit": "100",
		}

		if len(after) > 0 {
			queryParams["after"] = after
		}

		resp, err := sendOpenAIOrgRequest("GET", baseURL+"/organization/users", queryParams, headers, nil)
		if err != nil {
			return
		}

		var (
			usersResponse  UsersResponse
			usersList      []string
			userToEventMap = make(map[string]common.Event)
		)

		if err = json.Unmarshal(resp, &usersResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			break
		}

		for _, user := range usersResponse.Data {
			usersList = append(usersList, user.Email)
		}

		eventsQuery := `{"query":{"bool":{"must":[{"match":{"eventSource.keyword":"openai_audit_log"}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"accountId.keyword":"` + orgID + `"}},{"terms":{"username.keyword":["` + strings.Join(usersList, `","`) + `"]}}]}},"aggs":{"by_username":{"terms":{"field":"username.keyword","size":10000},"aggs":{"latest_event":{"top_hits":{"size":1,"sort":[{"eventTime":{"order":"desc"}}]}}}}},"size":0}`

		eventsAggregation, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_ACTIVITY_INDEX}, eventsQuery)
		if err != nil {
			return
		}

		eventsAggregationBytes, err := json.Marshal(eventsAggregation)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling raw aggregation", []string{tenantID}, err)
			return
		}

		var parsedResp EventsAggregationResponse
		if err := json.Unmarshal(eventsAggregationBytes, &parsedResp); err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling events aggregation response", []string{tenantID}, err)
			return
		}

		for _, bucket := range parsedResp.ByUsername.Buckets {
			if len(bucket.LatestEvent.Hits.Hits) == 0 {
				continue
			}

			eventDoc := bucket.LatestEvent.Hits.Hits[0].Source
			rctxEvent := common.Event{
				Name:   eventDoc.EventName,
				Region: "NA",
				Time:   eventDoc.EventTime,
			}

			userToEventMap[bucket.Key] = rctxEvent
		}

		for _, user := range usersResponse.Data {

			entityJson, err := json.Marshal(user)
			if err != nil {
				continue
			}

			aiResourcesDoc := common.AIResourcesDoc{
				EntityID:       user.ID,
				EntityType:     common.OPENAI_USER_RESOURCE_TYPE,
				AccountID:      orgID,
				StageCompleted: "dc",
				Deleted:        false,
				CollectedAt:    collectedAt,
				CreatedDate:    user.AddedAt,
				ServiceID:      common.OPENAI_SERVICE_ID_INT,
				TenantID:       tenantID,
				EntityJson:     string(entityJson),
			}

			if rctxEvent, ok := userToEventMap[user.Email]; ok {
				eventTime, err := elastic.ParseDateTime(rctxEvent.Time)
				if err != nil {
					continue
				}

				aiResourcesDoc.LastWriteTime = eventTime.UnixMilli()
				aiResourcesDoc.LastWriteActivity = &rctxEvent
			}

			aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
			usersAIResourceDocs = append(usersAIResourceDocs, aiResourcesDoc)
		}

		if usersResponse.HasMore {
			after = usersResponse.LastID
		} else {
			break
		}
	}

	if err := insertOpenAIResourceToDB(tenantID, common.OPENAI_USER_RESOURCE_TYPE, orgID, usersAIResourceDocs); err != nil {
		return
	}

	return
}

func collectAuditLogs(tenantID, token, orgID string, startTime, endTime time.Time) {
	var (
		after     string
		headers   = map[string]string{"Authorization": "Bearer " + token}
		eventsDoc []common.EventsDoc
	)

	if endTime.Sub(startTime) > eventMax {
		startTime = endTime.Add(-24 * time.Hour)
	}

	for {

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit":             "100",
			"effective_at[gte]": strconv.FormatInt(startTime.Unix(), 10),
			"effective_at[lt]":  strconv.FormatInt(endTime.Unix(), 10),
		}

		if len(after) > 0 {
			queryParams["after"] = after
		}

		resp, err := sendOpenAIOrgRequest("GET", baseURL+"/organization/audit_logs", queryParams, headers, nil)
		if err != nil {
			return
		}

		var auditLogsResponse AuditLogsResponse

		if err = json.Unmarshal(resp, &auditLogsResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			break
		}

		for _, auditLog := range auditLogsResponse.Data {

			if auditLogMap, ok := auditLog.(map[string]any); ok {

				eventID, _ := auditLogMap["id"].(string)
				eventType, _ := auditLogMap["type"].(string)
				eventTimeEpoch, _ := auditLogMap["effective_at"].(float64)

				if strings.HasPrefix(eventType, "invite") || strings.HasPrefix(eventType, "login") {
					continue
				}

				var resourceID, accountID, username, ipAddress, userAgent string

				if projectMap, ok := auditLogMap["project"].(map[string]any); ok {
					accountID, _ = projectMap["id"].(string)
				}

				if len(accountID) <= 0 {
					accountID = orgID
				}

				if eventTypeMap, ok := auditLogMap[eventType].(map[string]any); ok {
					resourceID, _ = eventTypeMap["id"].(string)
				}

				if len(resourceID) > 0 {

					if actor, ok := auditLogMap["actor"].(map[string]any); ok {

						if actorType, ok := actor["type"].(string); ok {

							switch actorType {

							case "session":

								if session, ok := actor["session"].(map[string]any); ok {
									if sessionUser, ok := session["user"].(map[string]any); ok {
										username, _ = sessionUser["email"].(string)
										if len(username) <= 0 {
											username, _ = sessionUser["id"].(string)
										}
									}

									ipAddress, _ = session["ip_address"].(string)
									userAgent, _ = session["user_agent"].(string)
								}

							case "api_key":

								if api_key, ok := actor["api_key"].(map[string]any); ok {

									if apiKeyType, ok := api_key["type"].(string); ok {

										switch apiKeyType {

										case "user":
											if apiKeyUser, ok := api_key["user"].(map[string]any); ok {
												username, _ = apiKeyUser["email"].(string)
												if len(username) <= 0 {
													username, _ = apiKeyUser["id"].(string)
												}
											}
										}
									}
								}
							}
						}
					}

					entityJson, err := json.Marshal(auditLogMap)
					if err != nil {
						continue
					}

					eventTime := time.Unix(int64(eventTimeEpoch), 0).UTC()

					eventDoc := common.EventsDoc{
						TenantID:    tenantID,
						AccountID:   accountID,
						ServiceCode: common.OPENAI_SERVICE_CODE,
						EventID:     eventID,
						EventName:   eventType,
						ReadOnly:    false,
						EventTime:   elastic.DateTime(eventTime),
						EventSource: "openai_audit_log",
						Username:    username,
						Resources: []common.EventResource{
							{
								ResourceName: resourceID,
							},
						},
						CloudTrailEvent: string(entityJson),
						SourceApp:       userAgent,
						IsUpdateEvent:   true,
						SourceIP:        ipAddress,
						EventStatus:     "1",
					}

					eventsDoc = append(eventsDoc, eventDoc)
				}
			}
		}

		if auditLogsResponse.HasMore {
			after = auditLogsResponse.LastID
		} else {
			break
		}
	}

	var (
		bulkEventsRequest, bulkIdentityRequest strings.Builder
		docCount                               int
		maxRecords                             = 1000
	)

	for _, eventDoc := range eventsDoc {

		eventDocID := common.GenerateCombinedHashID(eventDoc.EventID, tenantID)

		eventInsertMetadata := `{"index": {"_id": "` + eventDocID + `"}}`
		eventInsertDoc, err := json.Marshal(eventDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling document", err)
			return
		}

		bulkEventsRequest.WriteString(eventInsertMetadata)
		bulkEventsRequest.WriteString("\n")
		bulkEventsRequest.Write(eventInsertDoc)
		bulkEventsRequest.WriteString("\n")

		docCount++

		eventTime, err := elastic.ParseDateTime(eventDoc.EventTime)
		if err != nil {
			continue
		}

		identityDocID := common.GenerateCombinedHashIDCaseSensitive(tenantID, "openai", eventDoc.Username, eventDoc.AccountID, "OPEN_AI_USER")

		lastWriteObj := OpenAIEvent{
			Name:      eventDoc.EventName,
			Region:    "NA",
			Time:      eventDoc.EventTime,
			EpochTime: eventTime.UnixMilli(),
		}

		loginJson, err := json.Marshal(lastWriteObj)
		if err != nil {
			continue
		}

		identitiesUpdateMetadata := `{"update": {"_id": "` + identityDocID + `"}}`
		identityUpdateDoc := `{"doc":{"lastWriteActivity":` + string(loginJson) + `,"lastWriteTime":` + strconv.Itoa(int(lastWriteObj.EpochTime)) + `}}`

		bulkIdentityRequest.WriteString(identitiesUpdateMetadata)
		bulkIdentityRequest.WriteString("\n")
		bulkIdentityRequest.WriteString(identityUpdateDoc)
		bulkIdentityRequest.WriteString("\n")

		if docCount >= maxRecords {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_ACTIVITY_INDEX, bulkEventsRequest.String()); err != nil {
				return
			}

			if err := elastic.BulkDocumentsAPI(tenantID, elastic.IDENTITIES_INDEX, bulkIdentityRequest.String()); err != nil {
				return
			}

			docCount = 0
			bulkEventsRequest.Reset()
			bulkIdentityRequest.Reset()

			logger.Print(logger.INFO, "Identity bulk API Successful for "+strconv.Itoa(docCount)+" records", []string{tenantID})
			logger.Print(logger.INFO, "OpenAI events bulk API Successful for "+strconv.Itoa(docCount)+" records", []string{tenantID})
		}
	}

	if docCount > 0 {

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_ACTIVITY_INDEX, bulkEventsRequest.String()); err != nil {
			return
		}

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.IDENTITIES_INDEX, bulkIdentityRequest.String()); err != nil {
			return
		}

		logger.Print(logger.INFO, "OpenAI events bulk API Successful for "+strconv.Itoa(docCount)+" records", []string{tenantID})
	}

	return
}

func collectModels(aiMetaData *OpenAIMetaData, modelsMissingMetadata, modelsMissingBlockedStatus *[]string) {

	var (
		headers                                      = map[string]string{"Authorization": "Bearer " + aiMetaData.SaKey}
		modelsAIResourceDocs, ftModelsAIResourceDocs []common.AIResourcesDoc
		entityJson                                   []byte
		modelResponse                                OpenAIModelResponse
		entityType                                   = common.OPENAI_MODEL_RESOURCE_TYPE
		isModelBlockingEnabledInProject              = false
	)

	resp, err := sendOpenAIProjectRequest("GET", baseURL+"/models", nil, headers, nil, aiMetaData.ProjectID, aiMetaData.TenantID, true)
	if err != nil {
		return
	}

	if err = json.Unmarshal(resp, &modelResponse); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{aiMetaData.TenantID}, err)
		return
	}

	for _, model := range modelResponse.Data {

		if _, ok := StaticModelsBlockedStatus[model.ID]; ok {
			StaticModelsBlockedStatus[model.ID] = false
		} else if !strings.Contains(model.ID, "ft:") {
			if !slices.Contains(*modelsMissingBlockedStatus, model.ID) {
				*modelsMissingBlockedStatus = append(*modelsMissingBlockedStatus, model.ID)
			}
		}

		if strings.Contains(model.ID, "ft:") {
			// update model data with global project data first
			if modelData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[model.ID]; ok {
				description := map[string]any{}
				if err = json.Unmarshal([]byte(modelData.Description), &description); err == nil {
					model.Description = description["description"].(string)
					model.UseCase = description["useCase"].(string)
				}

				if aiMetaData.ProjectID == aiMetaData.DefaultProjectID {
					model.Stage = modelData.Stage
					model.DeploymentDate = modelData.DeploymentDate
					model.Assistants = modelData.Assistants
					model.Used = modelData.Used
				}
				model.TrainingFile = modelData.TrainingFile
				model.BaseModel = modelData.BaseModel
				model.ResultFiles = modelData.ResultFiles
				model.ValidationFile = modelData.ValidationFile
				model.TrainingInstability = modelData.TrainingInstability
				model.TrainingAccuracyFluctuation = modelData.TrainingAccuracyFluctuation
			}

			// update model data with project level data other than default values

			if projectSpecificModelData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[model.ID+"+"+aiMetaData.ProjectID]; ok {
				if len(projectSpecificModelData.TrainingFile) > 0 {
					model.TrainingFile = projectSpecificModelData.TrainingFile
				}
				if len(projectSpecificModelData.BaseModel) > 0 {
					model.BaseModel = projectSpecificModelData.BaseModel
				}
				if len(projectSpecificModelData.ResultFiles) > 0 {
					model.ResultFiles = projectSpecificModelData.ResultFiles
				}
				if len(projectSpecificModelData.ValidationFile) > 0 {
					model.ValidationFile = projectSpecificModelData.ValidationFile
				}

				model.Stage = projectSpecificModelData.Stage
				model.DeploymentDate = projectSpecificModelData.DeploymentDate
				model.Used = projectSpecificModelData.Used
				model.Assistants = projectSpecificModelData.Assistants
				model.TrainingInstability = projectSpecificModelData.TrainingInstability
				model.TrainingAccuracyFluctuation = projectSpecificModelData.TrainingAccuracyFluctuation

				description := map[string]any{}
				if err = json.Unmarshal([]byte(projectSpecificModelData.Description), &description); err == nil {
					if desc, ok := description["description"].(string); ok {
						model.Description = desc
					}
					if useCase, ok := description["useCase"].(string); ok {
						model.UseCase = useCase
					}
				}
				model.Batch1DayMaxInputTokens = projectSpecificModelData.Batch1DayMaxInputTokens
				model.MaxAudioMegabytesPerMin = projectSpecificModelData.MaxAudioMegabytesPerMin
				model.MaxRequestsPerMinute = projectSpecificModelData.MaxRequestsPerMinute
				model.MaxTokensPerMinute = projectSpecificModelData.MaxTokensPerMinute
				model.MaxImagesPerMinute = projectSpecificModelData.MaxImagesPerMinute
				model.MaxRequestsPerDay = projectSpecificModelData.MaxRequestsPerDay
			}

			if len(model.Stage) <= 0 {
				model.Stage = "Trained"
			}
			model.ModelType = "Fine-Tuned Model"
			entityType = common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE
		} else {

			if modelData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[model.ID]; ok {
				if aiMetaData.ProjectID == aiMetaData.DefaultProjectID {
					model.Stage = modelData.Stage
					model.DeploymentDate = modelData.DeploymentDate
					model.Assistants = modelData.Assistants
				}
			}

			if projectSpecificModelData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[model.ID+"+"+aiMetaData.ProjectID]; ok {
				model.Used = projectSpecificModelData.Used
				model.Stage = projectSpecificModelData.Stage
				model.DeploymentDate = projectSpecificModelData.DeploymentDate
				model.Assistants = projectSpecificModelData.Assistants

				model.Batch1DayMaxInputTokens = projectSpecificModelData.Batch1DayMaxInputTokens
				model.MaxAudioMegabytesPerMin = projectSpecificModelData.MaxAudioMegabytesPerMin
				model.MaxRequestsPerMinute = projectSpecificModelData.MaxRequestsPerMinute
				model.MaxTokensPerMinute = projectSpecificModelData.MaxTokensPerMinute
				model.MaxImagesPerMinute = projectSpecificModelData.MaxImagesPerMinute
				model.MaxRequestsPerDay = projectSpecificModelData.MaxRequestsPerDay
			}

			if len(model.Stage) <= 0 {
				model.Stage = "Pre-Trained"
			}
			model.ModelType = "Base Model"
		}

		var modelMap map[string]any
		inrec, _ := json.Marshal(model)
		json.Unmarshal(inrec, &modelMap)

		addStaticModelMetaData(modelMap, aiMetaData.StaticModelMetaData, modelsMissingMetadata)

		entityJson, err = json.Marshal(modelMap)
		if err != nil {
			continue
		}

		aiResourcesDoc := common.AIResourcesDoc{
			EntityID:       model.ID,
			EntityType:     entityType,
			AccountID:      aiMetaData.ProjectID,
			StageCompleted: "dc",
			Deleted:        false,
			CollectedAt:    aiMetaData.CollectedAt,
			CreatedDate:    model.Created,
			ServiceID:      common.OPENAI_SERVICE_ID_INT,
			TenantID:       aiMetaData.TenantID,
			EntityJson:     string(entityJson),
		}

		aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)

		if entityType == common.OPENAI_MODEL_RESOURCE_TYPE {
			modelsAIResourceDocs = append(modelsAIResourceDocs, aiResourcesDoc)
		} else {
			ftModelsAIResourceDocs = append(ftModelsAIResourceDocs, aiResourcesDoc)
		}
	}

	for modelName, blocked := range StaticModelsBlockedStatus {
		if !blocked {
			continue
		}

		modelMap := make(map[string]any)
		modelMap["isBlocked"] = true
		isModelBlockingEnabledInProject = true

		entityJson, err = json.Marshal(modelMap)
		if err != nil {
			continue
		}

		aiResourcesDoc := common.AIResourcesDoc{
			EntityID:       modelName,
			EntityType:     common.OPENAI_MODEL_RESOURCE_TYPE,
			AccountID:      aiMetaData.ProjectID,
			StageCompleted: "dc",
			Deleted:        true,
			CollectedAt:    aiMetaData.CollectedAt,
			CreatedDate:    0,
			ServiceID:      common.OPENAI_SERVICE_ID_INT,
			TenantID:       aiMetaData.TenantID,
			EntityJson:     string(entityJson),
		}

		aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
		modelsAIResourceDocs = append(modelsAIResourceDocs, aiResourcesDoc)
	}

	if err = insertOpenAIResourceToDB(aiMetaData.TenantID, common.OPENAI_MODEL_RESOURCE_TYPE, aiMetaData.ProjectID, modelsAIResourceDocs); err != nil {
		return
	}

	if err = insertOpenAIResourceToDB(aiMetaData.TenantID, common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE, aiMetaData.ProjectID, ftModelsAIResourceDocs); err != nil {
		return
	}

	// update the project with isModelBlockingEnabledInProject flag in json

	if isModelBlockingEnabledInProject {
		aiResourcesQuery := `{"query":{"bool":{"must":[{"wildcard":{"tenantId.keyword":"` + aiMetaData.TenantID + `"}},{"match":{"entityType.keyword":"openAIProject"}},{"match":{"entityId.keyword":"` + aiMetaData.ProjectID + `"}}]}}}`
		aiRscDocs, err := elastic.ExecuteSearchQuery([]string{elastic.AI_RESOURCES_INDEX}, aiResourcesQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Got error fetching project", []string{aiMetaData.TenantID}, err)
			return
		}

		if len(aiRscDocs) > 0 {

			for _, aiRscDoc := range aiRscDocs {

				if id, ok := aiRscDoc["id"].(string); ok {

					if entityJsonStr, ok := aiRscDoc["entityJson"].(string); ok {

						entityJson := make(map[string]any)
						if err := json.Unmarshal([]byte(entityJsonStr), &entityJson); err != nil {
							logger.Print(logger.ERROR, "Error unmarshalling project ai resource json", []string{aiMetaData.TenantID}, err)
							continue
						}

						entityJson["isModelBlockingEnabled"] = isModelBlockingEnabledInProject

						entityJsonBytes, err := json.Marshal(entityJson)
						if err != nil {
							logger.Print(logger.ERROR, "Error marshalling updated entityJson", []string{aiMetaData.TenantID}, err)
							continue
						}

						updateMeta := map[string]map[string]string{"update": {"_id": id}}
						updateDoc := map[string]map[string]string{"doc": {"entityJson": string(entityJsonBytes)}}

						metaLine, err := json.Marshal(updateMeta)
						if err != nil {
							logger.Print(logger.ERROR, "Error marshalling update metadata", []string{aiMetaData.TenantID}, err)
							continue
						}

						docLine, err := json.Marshal(updateDoc)
						if err != nil {
							logger.Print(logger.ERROR, "Error marshalling doc update", []string{aiMetaData.TenantID}, err)
							continue
						}

						bulkCloudResourceRequest := string(metaLine) + "\n" + string(docLine) + "\n"

						if err := elastic.BulkDocumentsAPI(aiMetaData.TenantID, elastic.AI_RESOURCES_INDEX, bulkCloudResourceRequest); err != nil {
							logger.Print(logger.ERROR, "Error in update of AI resources project doc", []string{aiMetaData.TenantID}, err, bulkCloudResourceRequest)
							return
						}
					}
				}
			}
		}
	}

	return
}

func collectBatches(aiMetaData *OpenAIMetaData) {

	var (
		after               string
		headers             = map[string]string{"Authorization": "Bearer " + aiMetaData.SaKey}
		batchAIResourceDocs []common.AIResourcesDoc
	)

	for {

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit": "100",
		}

		if len(after) > 0 {
			queryParams["after"] = after
		}

		resp, err := sendOpenAIProjectRequest("GET", baseURL+"/batches", queryParams, headers, nil, aiMetaData.ProjectID, aiMetaData.TenantID, true)
		if err != nil {
			return
		}

		var batchResponse OpenAIBatchResponse

		if err = json.Unmarshal(resp, &batchResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{aiMetaData.TenantID}, err)
			break
		}

		for _, batch := range batchResponse.Data {

			entityJson, err := json.Marshal(batch)
			if err != nil {
				continue
			}

			aiResourcesDoc := common.AIResourcesDoc{
				EntityID:       batch.ID,
				EntityType:     common.OPENAI_BATCH_RESOURCE_TYPE,
				AccountID:      aiMetaData.ProjectID,
				StageCompleted: "dc",
				Deleted:        false,
				CollectedAt:    aiMetaData.CollectedAt,
				CreatedDate:    batch.CreatedAt,
				ServiceID:      common.OPENAI_SERVICE_ID_INT,
				TenantID:       aiMetaData.TenantID,
				EntityJson:     string(entityJson),
			}

			aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
			batchAIResourceDocs = append(batchAIResourceDocs, aiResourcesDoc)
		}

		if batchResponse.HasMore {
			after = batchResponse.LastID
		} else {
			break
		}
	}

	if err := insertOpenAIResourceToDB(aiMetaData.TenantID, common.OPENAI_BATCH_RESOURCE_TYPE, aiMetaData.ProjectID, batchAIResourceDocs); err != nil {
		return
	}

	// TODO: Process batches To identify stage of model
	return
}

func collectAssistants(aiMetaData *OpenAIMetaData) {

	var (
		after   string
		headers = map[string]string{
			"Authorization": "Bearer " + aiMetaData.SaKey,
			"OpenAI-Beta":   "assistants=v2",
		}
		assistantsAIResourceDocs []common.AIResourcesDoc
	)

	for {

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit": "100",
		}

		if len(after) > 0 {
			queryParams["after"] = after
		}

		resp, err := sendOpenAIProjectRequest("GET", baseURL+"/assistants", queryParams, headers, nil, aiMetaData.ProjectID, aiMetaData.TenantID, true)
		if err != nil {
			return
		}

		var assistantResponse OpenAIAssistantResponse

		if err = json.Unmarshal(resp, &assistantResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{aiMetaData.TenantID}, err)
			break
		}

		for _, assistant := range assistantResponse.Data {

			modelMetaData := ModelMetaData{
				ModelID:        assistant.Model,
				Stage:          "Deployed",
				DeploymentDate: assistant.CreatedAt,
				Used:           true,
				Assistants:     []string{assistant.ID},
			}

			if modelsMetaData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[assistant.Model+"+"+aiMetaData.ProjectID]; ok {
				modelsMetaData.Stage = "Deployed"
				if assistant.CreatedAt > modelsMetaData.DeploymentDate {
					modelsMetaData.DeploymentDate = assistant.CreatedAt
				}
				modelsMetaData.Used = true
				modelsMetaData.Assistants = append(modelsMetaData.Assistants, assistant.ID)
				aiMetaData.AIMetaDataMap.ModelMetaData[assistant.Model+"+"+aiMetaData.ProjectID] = modelsMetaData
			} else {
				aiMetaData.AIMetaDataMap.ModelMetaData[assistant.Model+"+"+aiMetaData.ProjectID] = modelMetaData
			}

			if len(assistant.Description) <= 0 {
				if assistantDesc, err := common.GenerateAssistantDescriptionFromNameandInstructions(assistant.Name, assistant.Instructions, aiMetaData.TenantID); err == nil {
					assistant.Description = assistantDesc
				}
			}

			entityJson, err := json.Marshal(assistant)
			if err != nil {
				continue
			}

			aiResourcesDoc := common.AIResourcesDoc{
				EntityID:       assistant.ID,
				EntityType:     common.OPENAI_ASSISTANT_RESOURCE_TYPE,
				AccountID:      aiMetaData.ProjectID,
				StageCompleted: "dc",
				Deleted:        false,
				CollectedAt:    aiMetaData.CollectedAt,
				CreatedDate:    assistant.CreatedAt,
				ServiceID:      common.OPENAI_SERVICE_ID_INT,
				TenantID:       aiMetaData.TenantID,
				EntityJson:     string(entityJson),
			}

			aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
			assistantsAIResourceDocs = append(assistantsAIResourceDocs, aiResourcesDoc)
		}

		if assistantResponse.HasMore {
			after = assistantResponse.LastID
		} else {
			break
		}
	}

	if err := insertOpenAIResourceToDB(aiMetaData.TenantID, common.OPENAI_ASSISTANT_RESOURCE_TYPE, aiMetaData.ProjectID, assistantsAIResourceDocs); err != nil {
		return
	}

	return
}

func collectFineTuningData(aiMetaData *OpenAIMetaData) {

	var (
		after                        string
		headers                      = map[string]string{"Authorization": "Bearer " + aiMetaData.SaKey}
		fineTuningJobsAIResourceDocs []common.AIResourcesDoc
		fineTuningJobIDs             []string
	)

	for {

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit": "100",
		}

		if len(after) > 0 {
			queryParams["after"] = after
		}

		resp, err := sendOpenAIProjectRequest("GET", baseURL+"/fine_tuning/jobs", queryParams, headers, nil, aiMetaData.ProjectID, aiMetaData.TenantID, true)
		if err != nil {
			return
		}

		var fineTuningJobResponse OpenAIFineTuningJobResponse

		if err = json.Unmarshal(resp, &fineTuningJobResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{aiMetaData.TenantID}, err)
			break
		}

		for _, fineTuningJob := range fineTuningJobResponse.Data {

			fineTuningJobIDs = append(fineTuningJobIDs, fineTuningJob.ID)

			entityJson, err := json.Marshal(fineTuningJob)
			if err != nil {
				continue
			}

			if fineTuningJob.Status != "failed" && len(fineTuningJob.FineTunedModel) > 0 {
				modelMetaData := ModelMetaData{
					TrainingFile: fineTuningJob.TrainingFile,
					BaseModel:    fineTuningJob.Model,
					ResultFiles:  fineTuningJob.ResultFiles,
				}

				fineTuningJobMetaData := FineTuningJobMetaData{
					TrainingFile: fineTuningJob.TrainingFile,
					BaseModel:    fineTuningJob.Model,
				}

				fineTunedModelMetaDataKey := fineTuningJob.FineTunedModel

				if aiMetaData.ProjectID != aiMetaData.DefaultProjectID {
					fineTunedModelMetaDataKey += "+" + aiMetaData.ProjectID
				}

				// models in project other than default project is available only under that project
				if len(fineTuningJob.ValidationFile) > 0 {
					modelMetaData.ValidationFile = fineTuningJob.ValidationFile
					fineTuningJobMetaData.ValidationFile = fineTuningJob.ValidationFile
				}

				if modelsMetaData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[fineTunedModelMetaDataKey]; ok {
					modelsMetaData.TrainingFile = fineTuningJob.TrainingFile
					modelsMetaData.BaseModel = fineTuningJob.Model
					modelsMetaData.ResultFiles = fineTuningJob.ResultFiles
					modelsMetaData.ValidationFile = modelMetaData.ValidationFile
					modelsMetaData.Used = true
					aiMetaData.AIMetaDataMap.ModelMetaData[fineTunedModelMetaDataKey] = modelsMetaData
				} else {
					aiMetaData.AIMetaDataMap.ModelMetaData[fineTunedModelMetaDataKey] = modelMetaData
				}

				// add base model as in use
				if baseModelMetaData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[fineTuningJob.Model+"+"+aiMetaData.ProjectID]; ok {
					baseModelMetaData.Used = true
					baseModelMetaData.ModelID = fineTuningJob.Model

					aiMetaData.AIMetaDataMap.ModelMetaData[fineTuningJob.Model+"+"+aiMetaData.ProjectID] = baseModelMetaData
				} else {
					modelMetaData := ModelMetaData{
						ModelID: fineTuningJob.Model,
						Used:    true,
					}
					aiMetaData.AIMetaDataMap.ModelMetaData[fineTuningJob.Model+"+"+aiMetaData.ProjectID] = modelMetaData
				}

				// jobs metadata is restricted to project level
				if jobsMetaData, ok := aiMetaData.AIMetaDataMap.FineTuningJobMetaData[fineTuningJob.ID+"+"+aiMetaData.ProjectID]; ok {
					jobsMetaData.TrainingFile = fineTuningJobMetaData.TrainingFile
					jobsMetaData.BaseModel = fineTuningJobMetaData.BaseModel
					jobsMetaData.ValidationFile = fineTuningJobMetaData.ValidationFile
					aiMetaData.AIMetaDataMap.FineTuningJobMetaData[fineTuningJob.ID+"+"+aiMetaData.ProjectID] = jobsMetaData
				} else {
					aiMetaData.AIMetaDataMap.FineTuningJobMetaData[fineTuningJob.ID+"+"+aiMetaData.ProjectID] = fineTuningJobMetaData
				}

				for _, resultFile := range fineTuningJob.ResultFiles {
					if _, ok := aiMetaData.AIMetaDataMap.FileMetaData[resultFile+"+"+aiMetaData.ProjectID]; !ok {
						fileMeta := FileMetaData{
							Type: "Result File",
						}
						aiMetaData.AIMetaDataMap.FileMetaData[resultFile+"+"+aiMetaData.ProjectID] = fileMeta
					}
				}

			}

			// files are restricted to project level
			if len(fineTuningJob.TrainingFile) > 0 {
				if _, ok := aiMetaData.AIMetaDataMap.FileMetaData[fineTuningJob.TrainingFile+"+"+aiMetaData.ProjectID]; !ok {
					fileMeta := FileMetaData{
						Type: "Training File",
					}
					aiMetaData.AIMetaDataMap.FileMetaData[fineTuningJob.TrainingFile+"+"+aiMetaData.ProjectID] = fileMeta
				}
			}

			if len(fineTuningJob.ValidationFile) > 0 {
				if _, ok := aiMetaData.AIMetaDataMap.FileMetaData[fineTuningJob.ValidationFile+"+"+aiMetaData.ProjectID]; !ok {
					fileMeta := FileMetaData{
						Type: "Validation File",
					}
					aiMetaData.AIMetaDataMap.FileMetaData[fineTuningJob.ValidationFile+"+"+aiMetaData.ProjectID] = fileMeta
				}
			}

			aiResourcesDoc := common.AIResourcesDoc{
				EntityID:       fineTuningJob.ID,
				EntityType:     common.OPENAI_TUNINGJOB_RESOURCE_TYPE,
				AccountID:      aiMetaData.ProjectID,
				StageCompleted: "dc",
				Deleted:        false,
				CollectedAt:    aiMetaData.CollectedAt,
				CreatedDate:    fineTuningJob.CreatedAt,
				ServiceID:      common.OPENAI_SERVICE_ID_INT,
				TenantID:       aiMetaData.TenantID,
				EntityJson:     string(entityJson),
			}

			aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
			fineTuningJobsAIResourceDocs = append(fineTuningJobsAIResourceDocs, aiResourcesDoc)

			// Finetuning API's dont give last Id as of now, so we need to fetch the last Id
			after = fineTuningJob.ID
		}

		if fineTuningJobResponse.HasMore {
			if len(fineTuningJobResponse.LastID) > 0 { // Else we have backup assignment above
				after = fineTuningJobResponse.LastID
			}
		} else {
			break
		}
	}

	// collectFineTuningEvents(fineTuningJobIDs, aiMetaData)
	collectFineTuningJobCheckpoints(fineTuningJobIDs, aiMetaData)

	fineTuningJobsAIResourceInsertDocs := make([]common.AIResourcesDoc, 0)

	for _, fineTuningJobsAIResourceDoc := range fineTuningJobsAIResourceDocs {
		fineTuneJobJsonStr := fineTuningJobsAIResourceDoc.EntityJson
		var ftJob FineTuningJob
		err := json.Unmarshal([]byte(fineTuneJobJsonStr), &ftJob)
		if err != nil {
			return
		}

		if fineTuningJobMetaData, ok := aiMetaData.AIMetaDataMap.FineTuningJobMetaData[ftJob.ID+"+"+aiMetaData.ProjectID]; ok {
			if len(fineTuningJobMetaData.CheckpointModels) > 0 {
				ftJob.CheckpointModels = fineTuningJobMetaData.CheckpointModels
			}
		}

		entityJson, err := json.Marshal(ftJob)
		if err != nil {
			return
		}
		fineTuningJobsAIResourceDoc.EntityJson = string(entityJson)
		fineTuningJobsAIResourceInsertDocs = append(fineTuningJobsAIResourceInsertDocs, fineTuningJobsAIResourceDoc)
	}

	if err := insertOpenAIResourceToDB(aiMetaData.TenantID, common.OPENAI_TUNINGJOB_RESOURCE_TYPE, aiMetaData.ProjectID, fineTuningJobsAIResourceInsertDocs); err != nil {
		return
	}

	return
}

// Not being used
// func collectFineTuningEvents(fineTuningJobIDs []string, aiMetaData *OpenAIMetaData) {

// 	var (
// 		headers                           = map[string]string{"Authorization": "Bearer " + aiMetaData.SaKey}
// 		fineTuningJobEventsAIResourceDocs []common.AIResourcesDoc
// 	)

// 	for _, fineTuningJobID := range fineTuningJobIDs {

// 		var after string

// 		for {

// 			queryParams := make(map[string]string)
// 			queryParams = map[string]string{
// 				"limit": "100",
// 			}

// 			if len(after) > 0 {
// 				queryParams["after"] = after
// 			}

// 			resp, err := sendOpenAIProjectRequest("GET", baseURL+"/fine_tuning/jobs/"+fineTuningJobID+"/events", queryParams, headers, nil, aiMetaData.ProjectID, aiMetaData.TenantID, true)
// 			if err != nil {
// 				break
// 			}

// 			var jobEventResponse OpenAIFineTuningJobEventResponse

// 			if err = json.Unmarshal(resp, &jobEventResponse); err != nil {
// 				logger.Print(logger.ERROR, "Failed to unmarshal", []string{aiMetaData.TenantID}, err)
// 				break
// 			}

// 			for _, fineTuningJobEvent := range jobEventResponse.Data {

// 				entityJson, err := json.Marshal(fineTuningJobEvent)
// 				if err != nil {
// 					continue
// 				}

// 				aiResourcesDoc := common.AIResourcesDoc{
// 					EntityID:       fineTuningJobEvent.ID,
// 					EntityType:     common.OPENAI_TUNINGJOBEVENT_RESOURCE_TYPE,
// 					AccountID:      aiMetaData.ProjectID,
// 					StageCompleted: "dc",
// 					Deleted:        false,
// 					CollectedAt:    aiMetaData.CollectedAt,
// 					CreatedDate:    fineTuningJobEvent.CreatedAt,
// 					ServiceID:      common.OPENAI_SERVICE_ID_INT,
// 					TenantID:       aiMetaData.TenantID,
// 					EntityJson:     string(entityJson),
// 				}

// 				aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
// 				fineTuningJobEventsAIResourceDocs = append(fineTuningJobEventsAIResourceDocs, aiResourcesDoc)

// 				// Finetuning API's dont give last Id as of now, so we need to fetch the last Id
// 				after = fineTuningJobEvent.ID
// 			}

// 			if jobEventResponse.HasMore {
// 				if len(jobEventResponse.LastID) > 0 { // Else we have backup assignment above
// 					after = jobEventResponse.LastID
// 				}
// 			} else {
// 				break
// 			}
// 		}
// 	}

// 	if err := insertOpenAIResourceToDB(aiMetaData.TenantID, common.OPENAI_TUNINGJOBEVENT_RESOURCE_TYPE, aiMetaData.ProjectID, fineTuningJobEventsAIResourceDocs); err != nil {
// 		return
// 	}

// 	return
// }

func collectFineTuningJobCheckpoints(fineTuningJobIDs []string, aiMetaData *OpenAIMetaData) {

	var (
		headers                                = map[string]string{"Authorization": "Bearer " + aiMetaData.SaKey}
		fineTuningJobCheckpointsAIResourceDocs []common.AIResourcesDoc
	)

	for _, fineTuningJobID := range fineTuningJobIDs {

		var after string

		for {

			queryParams := make(map[string]string)
			queryParams = map[string]string{
				"limit": "100",
			}

			if len(after) > 0 {
				queryParams["after"] = after
			}

			resp, err := sendOpenAIProjectRequest("GET", baseURL+"/fine_tuning/jobs/"+fineTuningJobID+"/checkpoints", queryParams, headers, nil, aiMetaData.ProjectID, aiMetaData.TenantID, true)
			if err != nil {
				break
			}

			var jobCheckpointResponse OpenAIFineTuningJobCheckpointResponse

			if err = json.Unmarshal(resp, &jobCheckpointResponse); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", []string{aiMetaData.TenantID}, err)
				break
			}

			for _, fineTuningJobCheckpoint := range jobCheckpointResponse.Data {

				entityJson, err := json.Marshal(fineTuningJobCheckpoint)
				if err != nil {
					continue
				}

				if fineTuningJobMetaData, ok := aiMetaData.AIMetaDataMap.FineTuningJobMetaData[fineTuningJobCheckpoint.FineTuningJobID+"+"+aiMetaData.ProjectID]; ok {
					modelMetaData := ModelMetaData{
						TrainingFile:   fineTuningJobMetaData.TrainingFile,
						BaseModel:      fineTuningJobMetaData.BaseModel,
						ValidationFile: fineTuningJobMetaData.ValidationFile,
					}

					if aiMetaData.ProjectID == aiMetaData.DefaultProjectID {
						if modelsMetaData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[fineTuningJobCheckpoint.FineTunedModelCheckpoint]; ok {
							modelsMetaData.TrainingFile = modelMetaData.TrainingFile
							modelsMetaData.BaseModel = modelMetaData.BaseModel
							modelsMetaData.ValidationFile = modelMetaData.ValidationFile
							aiMetaData.AIMetaDataMap.ModelMetaData[fineTuningJobCheckpoint.FineTunedModelCheckpoint] = modelsMetaData
						} else {
							aiMetaData.AIMetaDataMap.ModelMetaData[fineTuningJobCheckpoint.FineTunedModelCheckpoint] = modelMetaData
						}
					} else {
						if modelsMetaData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[fineTuningJobCheckpoint.FineTunedModelCheckpoint+"+"+aiMetaData.ProjectID]; ok {
							modelsMetaData.TrainingFile = modelMetaData.TrainingFile
							modelsMetaData.BaseModel = modelMetaData.BaseModel
							modelsMetaData.ValidationFile = modelMetaData.ValidationFile
							aiMetaData.AIMetaDataMap.ModelMetaData[fineTuningJobCheckpoint.FineTunedModelCheckpoint+"+"+aiMetaData.ProjectID] = modelsMetaData
						} else {
							aiMetaData.AIMetaDataMap.ModelMetaData[fineTuningJobCheckpoint.FineTunedModelCheckpoint+"+"+aiMetaData.ProjectID] = modelMetaData
						}
					}

					fineTuningJobMetaData.CheckpointModels = append(fineTuningJobMetaData.CheckpointModels, fineTuningJobCheckpoint.FineTunedModelCheckpoint)
					aiMetaData.AIMetaDataMap.FineTuningJobMetaData[fineTuningJobCheckpoint.FineTuningJobID+"+"+aiMetaData.ProjectID] = fineTuningJobMetaData
				}

				aiResourcesDoc := common.AIResourcesDoc{
					EntityID:       fineTuningJobCheckpoint.ID,
					EntityType:     common.OPENAI_TUNINGJOBCHECKPOINT_RESOURCE_TYPE,
					AccountID:      aiMetaData.ProjectID,
					StageCompleted: "dc",
					Deleted:        false,
					CollectedAt:    aiMetaData.CollectedAt,
					CreatedDate:    fineTuningJobCheckpoint.CreatedAt,
					ServiceID:      common.OPENAI_SERVICE_ID_INT,
					TenantID:       aiMetaData.TenantID,
					EntityJson:     string(entityJson),
				}

				aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
				fineTuningJobCheckpointsAIResourceDocs = append(fineTuningJobCheckpointsAIResourceDocs, aiResourcesDoc)

				// Finetuning API's dont give last Id as of now, so we need to fetch the last Id
				after = fineTuningJobCheckpoint.ID
			}

			if jobCheckpointResponse.HasMore {
				if len(jobCheckpointResponse.LastID) > 0 { // Else we have backup assignment above
					after = jobCheckpointResponse.LastID
				}
			} else {
				break
			}
		}
	}

	if err := insertOpenAIResourceToDB(aiMetaData.TenantID, common.OPENAI_TUNINGJOBCHECKPOINT_RESOURCE_TYPE, aiMetaData.ProjectID, fineTuningJobCheckpointsAIResourceDocs); err != nil {
		return
	}

	return
}

func collectFiles(aiMetaData *OpenAIMetaData) {

	var (
		headers             = map[string]string{"Authorization": "Bearer " + aiMetaData.SaKey}
		filesAIResourceDocs []common.AIResourcesDoc
		uniqueFileIds       = make(map[string]struct{})
	)

	resp, err := sendOpenAIProjectRequest("GET", baseURL+"/files", nil, headers, nil, aiMetaData.ProjectID, aiMetaData.TenantID, true)
	if err != nil {
		return
	}

	var fileResponse OpenAIFileResponse

	if err = json.Unmarshal(resp, &fileResponse); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{aiMetaData.TenantID}, err)
		return
	}

	for _, file := range fileResponse.Data {

		if fileMetaData, ok := aiMetaData.AIMetaDataMap.FileMetaData[file.ID+"+"+aiMetaData.ProjectID]; ok {
			file.Type = fileMetaData.Type

			fileMetaData.Name = file.FileName
			fileMetaData.Bytes = file.Bytes
			aiMetaData.AIMetaDataMap.FileMetaData[file.ID+"+"+aiMetaData.ProjectID] = fileMetaData
		} else {
			fileMetaData := FileMetaData{
				Name:  file.FileName,
				Bytes: file.Bytes,
			}
			aiMetaData.AIMetaDataMap.FileMetaData[file.ID+"+"+aiMetaData.ProjectID] = fileMetaData
		}

		// if file.Purpose != "assistants" {

		// 	resp, err := sendOpenAIProjectRequest("GET", baseURL+"/files/"+file.ID+"/content", nil, headers, nil, aiMetaData.ProjectID, aiMetaData.TenantID, true)
		// 	if err != nil {
		// 		logger.Print(logger.INFO, "Assistant files are not downloadable", []string{aiMetaData.TenantID}, err)
		// 		continue
		// 	}

		// 	// trim large files
		// 	const maxSize = 1 * 1024 * 1024 // 1MB in bytes
		// 	if len(resp) > maxSize {
		// 		resp = resp[:maxSize]
		// 	}

		// 	fileCreatedTime := time.Unix(file.CreatedAt, 0)
		// 	if (fileCreatedTime.After(aiMetaData.startTime) || fileCreatedTime.Equal(aiMetaData.startTime)) &&
		// 		(fileCreatedTime.Before(aiMetaData.endTime) || fileCreatedTime.Equal(aiMetaData.endTime)) {

		// 		logger.Print(logger.INFO, "File Created recently (within past 24 hrs)",
		// 			[]string{aiMetaData.TenantID})

		// 		if err = deriveFileMetaDataFromFileContent(resp, file.ID, aiMetaData); err != nil {
		// 			logger.Print(logger.ERROR, "Failed to derive file meta data", []string{aiMetaData.TenantID}, file.ID, err)
		// 			if fileMetaData, ok := aiMetaData.AIMetaDataMap.FileMetaData[file.ID+"+"+aiMetaData.ProjectID]; ok {
		// 				file.HasPii = fileMetaData.HasPii
		// 				file.HasPci = fileMetaData.HasPci
		// 				file.HasPhi = fileMetaData.HasPhi
		// 			}
		// 		}
		// 	} else {
		// 		if sensitivityResp, err := common.DeriveSensitivityFromRawText(file.FileName+file.Purpose, aiMetaData.TenantID); err == nil && sensitivityResp != "" {

		// 			contentTags := map[string]any{}
		// 			if err = json.Unmarshal([]byte(sensitivityResp), &contentTags); err != nil {
		// 				logger.Print(logger.INFO, "Unable to Generate Model Tags for file", []string{aiMetaData.TenantID}, sensitivityResp)
		// 				return
		// 			}

		// 			if hasPii, ok := contentTags["HasPii"].(bool); ok {
		// 				file.HasPii = hasPii
		// 			}

		// 			if hasPci, ok := contentTags["HasPci"].(bool); ok {
		// 				file.HasPci = hasPci
		// 			}

		// 			if hasPhi, ok := contentTags["HasPhi"].(bool); ok {
		// 				file.HasPhi = hasPhi
		// 			}
		// 		}
		// 	}
		// } else {
		// 	if sensitivityResp, err := common.DeriveSensitivityFromRawText(file.FileName, aiMetaData.TenantID); err == nil && sensitivityResp != "" {

		// 		var contentTags map[string]any
		// 		if err = json.Unmarshal([]byte(sensitivityResp), &contentTags); err != nil {
		// 			logger.Print(logger.INFO, "Unable to Generate Model Tags for file", []string{aiMetaData.TenantID}, sensitivityResp)
		// 			return
		// 		}

		// 		if hasPii, ok := contentTags["HasPii"].(bool); ok {
		// 			file.HasPii = hasPii
		// 		} else {
		// 			if hasPii, ok := contentTags["HasPii"].(string); ok {
		// 				if hasPii == "true" {
		// 					file.HasPii = true
		// 				} else if hasPii == "false" {
		// 					file.HasPii = false
		// 				}
		// 			}
		// 		}

		// 		if hasPci, ok := contentTags["HasPci"].(bool); ok {
		// 			file.HasPci = hasPci
		// 		} else {
		// 			if hasPci, ok := contentTags["HasPci"].(string); ok {
		// 				if hasPci == "true" {
		// 					file.HasPci = true
		// 				} else if hasPci == "false" {
		// 					file.HasPci = false
		// 				}
		// 			}
		// 		}

		// 		if hasPhi, ok := contentTags["HasPhi"].(bool); ok {
		// 			file.HasPhi = hasPhi
		// 		} else {
		// 			if hasPhi, ok := contentTags["HasPhi"].(string); ok {
		// 				if hasPhi == "true" {
		// 					file.HasPhi = true
		// 				} else if hasPhi == "false" {
		// 					file.HasPhi = false
		// 				}
		// 			}
		// 		}

		// 	}
		// }

		if !file.HasPci && !file.HasPii && !file.HasPhi {
			file.SensitivityStatus = "Unknown"
		} else {
			file.SensitivityStatus = "Sensitive Data Present"
		}

		entityJson, err := json.Marshal(file)
		if err != nil {
			continue
		}

		aiResourcesDoc := common.AIResourcesDoc{
			EntityID:       file.ID,
			EntityType:     common.OPENAI_FILE_RESOURCE_TYPE,
			AccountID:      aiMetaData.ProjectID,
			StageCompleted: "dc",
			Deleted:        false,
			CollectedAt:    aiMetaData.CollectedAt,
			CreatedDate:    file.CreatedAt,
			ServiceID:      common.OPENAI_SERVICE_ID_INT,
			TenantID:       aiMetaData.TenantID,
			EntityJson:     string(entityJson),
		}

		aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
		filesAIResourceDocs = append(filesAIResourceDocs, aiResourcesDoc)
		uniqueFileIds[file.ID] = struct{}{}
	}

	if err = insertOpenAIResourceToDB(aiMetaData.TenantID, common.OPENAI_FILE_RESOURCE_TYPE, aiMetaData.ProjectID, filesAIResourceDocs); err != nil {
		return
	}

	// TODO: Get File content
	aiModelMetaData := aiMetaData.AIMetaDataMap.ModelMetaData
	if len(aiModelMetaData) > 0 {

		for modelKey, modelData := range aiModelMetaData {
			if _, ok := uniqueFileIds[modelData.TrainingFile]; ok && len(modelData.TrainingFile) > 0 {
				resp, err := sendOpenAIProjectRequest("GET", baseURL+"/files/"+modelData.TrainingFile+"/content", nil, headers, nil, aiMetaData.ProjectID, aiMetaData.TenantID, true)
				if err != nil {
					return
				}

				err = deriveModelDescriptionFromFileContent(resp, aiMetaData, modelData, modelKey, modelData.TrainingFile)
				if err != nil {
					return
				}

				err = derivedModelContentTagsFromFileContent(resp, aiMetaData, modelData, modelKey, modelData.TrainingFile)
				if err != nil {
					return
				}
			}

			if len(modelData.ResultFiles) > 0 {
				for _, resultFile := range modelData.ResultFiles {
					if _, ok := uniqueFileIds[resultFile]; ok && len(resultFile) > 0 {
						resp, err := sendOpenAIProjectRequest("GET", baseURL+"/files/"+resultFile+"/content", nil, headers, nil, aiMetaData.ProjectID, aiMetaData.TenantID, true)
						if err != nil {
							return
						}

						fileContentStr := string(resp)

						if !strings.Contains(fileContentStr, "train_loss") {
							fileContentStr, err = common.Base64Decode(fileContentStr)
							if err != nil {
								return
							}
						}

						var (
							records            []FineTuningResultFile
							trainingLoss       []float64
							trainingAccuracy   []float64
							validationLoss     []float64
							validationAccuracy []float64
						)
						if err = gocsv.UnmarshalString(fileContentStr, &records); err != nil {
							logger.Print(logger.ERROR, "Error conserting string to csv", []string{aiMetaData.TenantID}, resultFile, err)
						}

						for _, record := range records {
							if record.TrainAccuracy != 0.0 {
								trainingAccuracy = append(trainingAccuracy, record.TrainAccuracy)
							}

							if record.TrainLoss != 0.0 {
								trainingLoss = append(trainingLoss, record.TrainLoss)
							}

							if record.TrainAccuracy != 0.0 {
								validationLoss = append(validationLoss, record.ValidLoss)
							}

							if record.ValidMeanTokenAccuracy == 0.0 {
								validationAccuracy = append(validationAccuracy, -1.0)
							} else {
								validationAccuracy = append(validationAccuracy, record.ValidMeanTokenAccuracy)
							}
						}

						evaluateTrainingAccuracy(trainingAccuracy, validationAccuracy, aiMetaData, modelKey)
						evaluateTrainingLoss(trainingLoss, aiMetaData, modelKey)
					}
				}
			}
		}
	}

	return
}

func deriveModelDescriptionFromFileContent(resp []byte, aiMetaData *OpenAIMetaData, modelData ModelMetaData, modelKey, fileID string) (err error) {

	textLookupDocID := common.GenerateCombinedHashID(aiMetaData.TenantID, modelData.TrainingFile+"_"+aiMetaData.TenantID)
	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		desc, _ := doc["response"].(string)

		if modelsMetaData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[modelKey]; ok {
			modelsMetaData.Description = desc

			aiMetaData.AIMetaDataMap.ModelMetaData[modelKey] = modelsMetaData
		}
		return
	}

	if !strings.Contains(modelData.ModelID, "ft:") {
		return
	}

	openAIReqText := "Generate a description of the model which mentions the use-case of the model derived from the attached file. Follow the instructions and provide data authoritatively in below format. {\"description\" : \"provide description in one paragraph strictly. Description should have only text.\", \"useCase\" : \"4-10 words describing the use case\"}"
	messagesResp, err := common.RequestAIFileSupport(openAIReqText, fileID, aiMetaData.TenantID, resp)
	if err != nil {
		return
	}

	for _, msgData := range messagesResp.Data {

		for _, msg := range msgData.Content {
			modelDescription := msg.Text.Value

			description := map[string]any{}
			if err = json.Unmarshal([]byte(modelDescription), &description); err != nil {
				parsedTrainingFile, er := common.ParseTrainingFile(modelKey, aiMetaData.TenantID, resp)
				if er != nil {
					err = er
					return
				}
				desc := common.DeriveModelDescription(parsedTrainingFile, aiMetaData.TenantID)
				if err = json.Unmarshal([]byte(desc), &description); err != nil {
					logger.Print(logger.INFO, "Unable to Generate Model Description", []string{aiMetaData.TenantID})
					continue
				} else {
					modelDescription = desc
				}
			}

			if modelData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[modelKey]; ok {
				modelData.Description = modelDescription

				aiMetaData.AIMetaDataMap.ModelMetaData[modelKey] = modelData
			}

			if _, err = elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, common.TextLookupInsertDoc{
				Text:       modelData.TrainingFile + "_" + aiMetaData.TenantID,
				Response:   modelDescription,
				InsertTime: elastic.DateTime(time.Now().UTC()),
				TenantID:   aiMetaData.TenantID,
			}, textLookupDocID); err != nil {
				return
			}
		}
	}

	return
}

func addStaticModelMetaData(modelData, staticModelData map[string]any, modelsMissingMetadata *[]string) {
	if modelId, ok := modelData["id"].(string); ok {
		if modelStaticMetaData, ok := staticModelData[modelId].(map[string]any); ok {
			for key, value := range modelStaticMetaData {
				if _, ok := modelData[key]; !ok {
					modelData[key] = value
				}
			}
		} else if !strings.Contains(modelId, "ft:") && !slices.Contains(*modelsMissingMetadata, modelId) {
			*modelsMissingMetadata = append(*modelsMissingMetadata, modelId)
		}
	}
}

func derivedModelContentTagsFromFileContent(resp []byte, aiMetaData *OpenAIMetaData, modelData ModelMetaData, modelKey, fileID string) (err error) {

	textLookupDocID := common.GenerateCombinedHashID(aiMetaData.TenantID, modelData.TrainingFile+"_"+aiMetaData.TenantID+"_training_file_tag_content")
	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		modelTag, _ := doc["response"].(string)

		if modelsMetaData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[modelKey]; ok {
			contentTags := map[string]any{}

			if err = json.Unmarshal([]byte(modelTag), &contentTags); err == nil {
				if hasPii, ok := contentTags["HasPii"].(bool); ok {
					modelsMetaData.HasPii = hasPii
				}

				if hasPci, ok := contentTags["HasPci"].(bool); ok {
					modelsMetaData.HasPci = hasPci
				}

				if hasPhi, ok := contentTags["HasPhi"].(bool); ok {
					modelsMetaData.HasPii = hasPhi
				}
			}
			aiMetaData.AIMetaDataMap.ModelMetaData[modelKey] = modelsMetaData
		}
		return
	}

	if !strings.Contains(modelData.ModelID, "ft:") {
		return
	}

	openAIReqText := "Answer whether the attached file contains any Payment Card Industry (pci),Protected Health Information (Phi), Personally Identifiable Information (Pii) information in the specified format only. Follow the instructions, provide data in below format only and do not give any extra description. {\"HasPii\" : \"bool value\", \"HasPci\" : \"bool value\", \"HasPhi\" : \"bool value\"}"
	messagesResp, err := common.RequestAIFileSupport(openAIReqText, fileID, aiMetaData.TenantID, resp)
	if err != nil {
		return
	}

	for _, msgData := range messagesResp.Data {

		for _, msg := range msgData.Content {
			modelTrainingTags := msg.Text.Value

			contentTags := map[string]any{}
			if err = json.Unmarshal([]byte(modelTrainingTags), &contentTags); err != nil {
				parsedTrainingFile, er := common.ParseTrainingFile(modelKey, aiMetaData.TenantID, resp)
				if er != nil {
					err = er
					return
				}
				modelTrainingTags = common.DeriveModelTrainingFileTag(parsedTrainingFile, aiMetaData.TenantID)
				if err = json.Unmarshal([]byte(modelTrainingTags), &contentTags); err != nil {
					logger.Print(logger.INFO, "Unable to Generate Model Tags", []string{aiMetaData.TenantID})
					continue
				}
			}

			if modelData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[modelKey]; ok {
				if hasPii, ok := contentTags["HasPii"].(bool); ok {
					modelData.HasPii = hasPii
				}

				if hasPci, ok := contentTags["HasPci"].(bool); ok {
					modelData.HasPci = hasPci
				}

				if hasPhi, ok := contentTags["HasPhi"].(bool); ok {
					modelData.HasPii = hasPhi
				}
				aiMetaData.AIMetaDataMap.ModelMetaData[modelKey] = modelData
			}

			if _, err = elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, common.TextLookupInsertDoc{
				Text:       modelData.TrainingFile + "_" + aiMetaData.TenantID + "_" + "training_file_tag_content",
				Response:   modelTrainingTags,
				InsertTime: elastic.DateTime(time.Now().UTC()),
				TenantID:   aiMetaData.TenantID,
			}, textLookupDocID); err != nil {
				return
			}
		}
	}

	return
}

// Function not being used today
// func deriveFileMetaDataFromFileContent(resp []byte, fileID string, aiMetaData *OpenAIMetaData) (err error) {
// 	textLookupDocID := common.GenerateCombinedHashID(aiMetaData.TenantID, fileID+"_"+aiMetaData.TenantID+"_training_file_tag_content")
// 	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
// 		modelTag, _ := doc["response"].(string)

// 		if fileMetaData, ok := aiMetaData.AIMetaDataMap.FileMetaData[fileID+"+"+aiMetaData.ProjectID]; ok {
// 			contentTags := map[string]any{}

// 			if err = json.Unmarshal([]byte(modelTag), &contentTags); err == nil {
// 				if hasPii, ok := contentTags["HasPii"].(bool); ok {
// 					fileMetaData.HasPii = hasPii
// 				}

// 				if hasPci, ok := contentTags["HasPci"].(bool); ok {
// 					fileMetaData.HasPci = hasPci
// 				}

// 				if hasPhi, ok := contentTags["HasPhi"].(bool); ok {
// 					fileMetaData.HasPii = hasPhi
// 				}
// 			}
// 			aiMetaData.AIMetaDataMap.FileMetaData[fileID+"+"+aiMetaData.ProjectID] = fileMetaData
// 		}
// 		return
// 	}

// 	openAIReqText := "Answer whether the attached file contains any Payment Card Industry (pci),Protected Health Information (Phi), Personally Identifiable Information (Pii) information in the specified format only. Follow the instructions, provide data in below format only and do not give any extra description. {\"HasPii\" : \"bool value\", \"HasPci\" : \"bool value\", \"HasPhi\" : \"bool value\"}"
// 	messagesResp, err := common.RequestAIFileSupport(openAIReqText, fileID, aiMetaData.TenantID, resp)
// 	if err != nil {
// 		return
// 	}

// 	for _, msgData := range messagesResp.Data {

// 		for _, msg := range msgData.Content {
// 			modelTrainingTags := msg.Text.Value

// 			contentTags := map[string]any{}
// 			if err = json.Unmarshal([]byte(modelTrainingTags), &contentTags); err != nil {
// 				logger.Print(logger.INFO, "Unable to Generate Model Tags for file", []string{aiMetaData.TenantID}, fileID)
// 				continue
// 			}

// 			if fileMetaData, ok := aiMetaData.AIMetaDataMap.FileMetaData[fileID+"+"+aiMetaData.ProjectID]; ok {
// 				if hasPii, ok := contentTags["HasPii"].(bool); ok {
// 					fileMetaData.HasPii = hasPii
// 				}

// 				if hasPci, ok := contentTags["HasPci"].(bool); ok {
// 					fileMetaData.HasPci = hasPci
// 				}

// 				if hasPhi, ok := contentTags["HasPhi"].(bool); ok {
// 					fileMetaData.HasPii = hasPhi
// 				}
// 				aiMetaData.AIMetaDataMap.FileMetaData[fileID+"+"+aiMetaData.ProjectID] = fileMetaData
// 			}

// 			if _, err = elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, common.TextLookupInsertDoc{
// 				Text:       fileID + "_" + aiMetaData.TenantID + "_" + "training_file_tag_content",
// 				Response:   modelTrainingTags,
// 				InsertTime: elastic.DateTime(time.Now().UTC()),
// 				TenantID:   aiMetaData.TenantID,
// 				Category:   "training_file_tag_content",
// 			}, textLookupDocID); err != nil {
// 				return
// 			}
// 		}
// 	}

// 	return
// }

func evaluateTrainingAccuracy(trainingAccuracy, validationAccuracy []float64, aiMetaData *OpenAIMetaData, modelKey string) {

	var (
		trainingAccuracyFluctuation bool
	)

	_, fluctuationCount := calculateEpochAveragesAndFluctuations(trainingAccuracy, 10, 0.5)

	// training accuracy should ultimately increase
	if len(trainingAccuracy) > 0 {
		if trainingAccuracy[0] > trainingAccuracy[len(trainingAccuracy)-1] {
			trainingAccuracyFluctuation = true
		}
	}

	// during early stages the training accuracy should not exceed the validation accuracy by too much which may lead to overfitting
	earlyStageLimit := int(0.25 * float64(len(trainingAccuracy)))
	for i := 0; i < len(trainingAccuracy) && i < len(validationAccuracy); i++ {
		if i <= earlyStageLimit && validationAccuracy[i] != -1.0 && trainingAccuracy[i]-validationAccuracy[i] > 0.5 {
			trainingAccuracyFluctuation = true
		}
	}

	// there should not be much fluctuation in b/w groups
	if fluctuationCount > 4 {
		trainingAccuracyFluctuation = true
	}

	// check for training fluctuation using standard mean
	mean := common.CalculateMean(trainingAccuracy)

	if mean < 0.7 {
		for _, acc := range trainingAccuracy {
			difference := math.Abs(acc - mean)
			if difference > 0.2 {
				trainingAccuracyFluctuation = true
				break
			}
		}
	}

	if trainingAccuracyFluctuation {
		if modelData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[modelKey]; ok {
			modelData.TrainingAccuracyFluctuation = trainingAccuracyFluctuation

			aiMetaData.AIMetaDataMap.ModelMetaData[modelKey] = modelData
		}
	}
}

func evaluateTrainingLoss(trainingLoss []float64, aiMetaData *OpenAIMetaData, modelKey string) {

	var (
		trainingInstability bool
	)

	averages, fluctuationCount := calculateEpochAveragesAndFluctuations(trainingLoss, 10, 2.0)

	// training loss should ultimately descrease
	if len(trainingLoss) > 0 {
		if trainingLoss[0] < trainingLoss[len(trainingLoss)-1] {
			trainingInstability = true
		}
	}

	// 70% of the epochs should be descreasing
	decreasingCount := 0
	for i := 1; i < len(averages); i++ {
		if averages[i] < averages[i-1] {
			decreasingCount++
		}
	}

	decreasingPercentage := (float64(decreasingCount) / float64(len(averages)-1)) * 100
	if decreasingPercentage <= 70 {
		trainingInstability = true
	}

	// after the initial training phase there should not be large fluctuations in the training loss
	if fluctuationCount > 4 {
		trainingInstability = true
	}

	if trainingInstability {
		if modelData, ok := aiMetaData.AIMetaDataMap.ModelMetaData[modelKey]; ok {
			modelData.TrainingInstability = trainingInstability

			aiMetaData.AIMetaDataMap.ModelMetaData[modelKey] = modelData
		}
	}
}

func calculateEpochAveragesAndFluctuations(trainingData []float64, groupSize int, fluctuationVal float64) ([]float64, int) {
	var averages []float64
	fluctuationCount := 0

	totalGroups := (len(trainingData) + groupSize - 1) / groupSize
	startGroup := totalGroups / 4

	for i := 0; i < len(trainingData); i += groupSize {
		sum := 0.0
		count := 0
		minVal := trainingData[i]
		maxVal := trainingData[i]

		for j := i; j < i+groupSize && j < len(trainingData); j++ {
			sum += trainingData[j]
			count++
			if trainingData[j] < minVal {
				minVal = trainingData[j]
			}
			if trainingData[j] > maxVal {
				maxVal = trainingData[j]
			}
		}

		avg := sum / float64(count)
		averages = append(averages, avg)

		currentGroupIndex := i / groupSize
		if currentGroupIndex >= startGroup && maxVal-minVal > fluctuationVal {
			fluctuationCount++
		}
	}

	return averages, fluctuationCount
}

func insertProjectRecordForProjectOnb(projectID, tenantID string, collectedAt int64) {

	aiResourcesDoc := common.AIResourcesDoc{
		EntityID:       projectID,
		EntityType:     common.OPENAI_PROJECT_RESOURCE_TYPE,
		AccountID:      projectID,
		StageCompleted: "dc",
		Deleted:        false,
		CollectedAt:    collectedAt,
		CreatedDate:    collectedAt,
		ServiceID:      common.OPENAI_SERVICE_ID_INT,
		TenantID:       tenantID,
	}
	aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)

	query := `{"query":{"bool":{"must":[{"match":{"accountId.keyword":"` + projectID + `"}},{"match":{"deleted":"false"}}],"must_not":[{"match":{"entityType.keyword":"openAIProject"}}]}},"size":"1"}`
	aiRscDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.AI_RESOURCES_INDEX}, query)
	if err != nil {
		return
	}

	if len(aiRscDocs) == 0 {
		// collection might have error or child resources might have been deleted or project record actually not created
		query := `{"query":{"bool":{"must":[{"match":{"accountId.keyword":"` + projectID + `"}},{"match":{"deleted":"true"}}],"must_not":[{"match":{"entityType.keyword":"openAIProject"}}]}},"size":"1"}`
		aiRscDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.AI_RESOURCES_INDEX}, query)
		if err != nil {
			return
		}

		if len(aiRscDocs) > 0 {
			aiDoc, err := elastic.GetDocument(elastic.AI_RESOURCES_INDEX, aiResourcesDoc.ID)
			if err != nil {
				return
			}

			if len(aiDoc) > 0 {
				// all child resources for the project has been deleted
				if err = elastic.UpdateDocument(
					elastic.AI_RESOURCES_INDEX,
					aiResourcesDoc.ID,
					struct {
						Deleted bool `json:"deleted"`
					}{
						Deleted: true,
					},
				); err != nil {
					return
				}
			}
		} else {
			if _, err := elastic.InsertDocument(tenantID, elastic.AI_RESOURCES_INDEX, aiResourcesDoc, aiResourcesDoc.ID); err == nil {
				logger.Print(logger.INFO, "OpenAI Project creation Successful for "+aiResourcesDoc.EntityType, []string{tenantID, projectID})
			}
		}
	}
}

func collectUsage(aiMetaData *OpenAIMetaData, openAIStartTime, openAIEndTime time.Time) {

	// add 1 day buffer to get accurate data
	openAIEndTime = openAIEndTime.Add(-1 * 24 * time.Hour)

	openAIStartTime = openAIEndTime.Add(-31 * 24 * time.Hour)

	collectCompletionUsage(aiMetaData, openAIStartTime, openAIEndTime)
	// collectEmbeddingsUsage(aiMetaData, openAIStartTime, openAIEndTime)
	// collectModerationsUsage(aiMetaData, openAIStartTime, openAIEndTime)
	// collectImagesUsage(aiMetaData, openAIStartTime, openAIEndTime)
	// collectAudioSpeechUsage(aiMetaData, openAIStartTime, openAIEndTime)
	// collectAudioTranscriptionUsage(aiMetaData, openAIStartTime, openAIEndTime)
	// collectVectorStoreUsage(aiMetaData, openAIStartTime, openAIEndTime)
	collectOrgCost(aiMetaData, openAIStartTime, openAIEndTime)
	collectProjectCost(aiMetaData, openAIStartTime, openAIEndTime)

	calculateProjectCost(aiMetaData)
	//calculateOrgCost(aiMetaData)

}

func collectVectorStore(aiMetaData *OpenAIMetaData) (err error) {

	var (
		after   string
		headers = map[string]string{
			"Authorization": "Bearer " + aiMetaData.SaKey,
		}
		vsAIResourceDocs []common.AIResourcesDoc
	)

	for {

		logger.Print(logger.INFO, "Fetching Vector Stores", []string{aiMetaData.TenantID})

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit": "100",
		}

		if len(after) > 0 {
			queryParams["after"] = after
		}

		resp, err := sendOpenAIProjectRequest("GET", baseURL+"/vector_stores", queryParams, headers, nil, aiMetaData.ProjectID, aiMetaData.TenantID, true)
		if err != nil {
			return err
		}

		var vsResponse OpenAIVectorStoreResponse

		if err = json.Unmarshal(resp, &vsResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{aiMetaData.TenantID}, err)
			break
		}

		for _, vs := range vsResponse.Data {

			entityJson, err := json.Marshal(vs)
			if err != nil {
				continue
			}

			if vs.Status == "expired" {
				continue
			}

			aiResourcesDoc := common.AIResourcesDoc{
				EntityID:       vs.ID,
				EntityType:     common.OPENAI_VECTORSTORE_RESOURCE_TYPE,
				AccountID:      aiMetaData.ProjectID,
				StageCompleted: "dc",
				Deleted:        false,
				CollectedAt:    aiMetaData.CollectedAt,
				CreatedDate:    vs.CreatedAt,
				ServiceID:      common.OPENAI_SERVICE_ID_INT,
				TenantID:       aiMetaData.TenantID,
				EntityJson:     string(entityJson),
			}

			aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
			vsAIResourceDocs = append(vsAIResourceDocs, aiResourcesDoc)
		}

		if vsResponse.HasMore {
			after = vsResponse.LastID
		} else {
			break
		}
	}

	if err = insertOpenAIResourceToDB(aiMetaData.TenantID, common.OPENAI_VECTORSTORE_RESOURCE_TYPE, aiMetaData.ProjectID, vsAIResourceDocs); err != nil {
		return
	}

	return
}

func collectVectorStoreFiles(aiMetaData *OpenAIMetaData) {

	var (
		vsFileAIResourceDocs = make([]common.AIResourcesDoc, 0)
		headers              = map[string]string{
			"Authorization": "Bearer " + aiMetaData.SaKey,
		}

		searchAfter any
	)

	for {

		vectorStoreQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + aiMetaData.TenantID + `"}},{"match":{"entityType.keyword":"` + common.OPENAI_VECTORSTORE_RESOURCE_TYPE + `"}},{"match":{"accountId.keyword":"` + aiMetaData.ProjectID + `"}},{"match":{"deleted":"false"}}]}}}`
		vsDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.AI_RESOURCES_INDEX}, vectorStoreQuery, searchAfter)
		if err != nil {
			return
		}

		if len(vsDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		logger.Print(logger.INFO, "Starting Vector Store File Collection", []string{aiMetaData.TenantID})

		for _, vsDoc := range vsDocs {

			if entityJsonStr, ok := vsDoc["entityJson"].(string); ok {

				var vectorStoreDoc VectorStore
				if err = json.Unmarshal([]byte(entityJsonStr), &vectorStoreDoc); err != nil {
					logger.Print(logger.ERROR, "Error unmarshalling vector store JSON", []string{aiMetaData.TenantID}, err)
					continue
				}

				// vector stores with 0 files are not considered
				if vectorStoreDoc.FileCounts.Total <= 0 {
					continue
				}

				if vectorID, ok := vsDoc["entityId"].(string); ok {

					var (
						after string
					)

					for {

						queryParams := make(map[string]string)
						queryParams = map[string]string{
							"limit": "100",
						}

						if len(after) > 0 {
							queryParams["after"] = after
						}

						resp, err := sendOpenAIProjectRequest("GET", baseURL+"/vector_stores/"+vectorID+"/files", queryParams, headers, nil, aiMetaData.ProjectID, aiMetaData.TenantID, true)
						if err != nil {
							continue
						}

						var vsFileResponse OpenAIVectorStoreFileResponse

						if err = json.Unmarshal(resp, &vsFileResponse); err != nil {
							logger.Print(logger.ERROR, "Failed to unmarshal", []string{aiMetaData.TenantID}, err)
							break
						}

						for _, vsFile := range vsFileResponse.Data {

							if fileMetaData, ok := aiMetaData.AIMetaDataMap.FileMetaData[vsFile.ID+"+"+aiMetaData.ProjectID]; ok {
								vsFile.Name = fileMetaData.Name
								vsFile.Bytes = fileMetaData.Bytes
							}

							entityJson, err := json.Marshal(vsFile)
							if err != nil {
								continue
							}

							aiResourcesDoc := common.AIResourcesDoc{
								EntityID:       vsFile.ID,
								EntityType:     common.OPENAI_VECTORSTOREFILE_RESOURCE_TYPE,
								AccountID:      aiMetaData.ProjectID,
								StageCompleted: "dc",
								Deleted:        false,
								CollectedAt:    aiMetaData.CollectedAt,
								CreatedDate:    vsFile.CreatedAt,
								ServiceID:      common.OPENAI_SERVICE_ID_INT,
								TenantID:       aiMetaData.TenantID,
								EntityJson:     string(entityJson),
							}

							aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID, aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
							vsFileAIResourceDocs = append(vsFileAIResourceDocs, aiResourcesDoc)
						}

						if vsFileResponse.HasMore {
							after = vsFileResponse.LastID
						} else {
							break
						}
					}
				}
			}
		}
	}

	if err := insertOpenAIResourceToDB(aiMetaData.TenantID, common.OPENAI_VECTORSTOREFILE_RESOURCE_TYPE, aiMetaData.ProjectID, vsFileAIResourceDocs); err != nil {
		return
	}

	// Delete openAI_File records created for openAI_VectorStoreFile
	var (
		deletedFileIDs = make([]string, 0)
	)

	for _, vsFileDoc := range vsFileAIResourceDocs {
		deletedFileIDs = append(deletedFileIDs, vsFileDoc.EntityID)
	}

	query := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + aiMetaData.TenantID + `"}},{"match":{"entityType.keyword":"` + common.OPENAI_FILE_RESOURCE_TYPE + `"}},{"match":{"accountId.keyword":"` + aiMetaData.ProjectID + `"}},{"terms":{"entityId.keyword":["` + strings.Join(deletedFileIDs, `","`) + `"]}}]}}}`
	err := elastic.DeleteByQuery(elastic.AI_RESOURCES_INDEX, query)
	if err != nil {
		return
	}

	return
}

func calculateProjectCost(aiMetaData *OpenAIMetaData) {

	projectQuery := `{"query":{"bool":{"must":[{"wildcard":{"tenantId.keyword":"` + aiMetaData.TenantID + `"}},{"match":{"entityType.keyword":"openAIProject"}},{"match":{"deleted":"false"}}]}}}`
	projDocs, err := elastic.ExecuteSearchQuery([]string{elastic.AI_RESOURCES_INDEX}, projectQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Got error fetching project", []string{aiMetaData.TenantID}, err)
		return
	}

	for _, projDoc := range projDocs {
		if projID, ok := projDoc["entityId"].(string); ok {

			// accumulate project cost
			projCostQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + aiMetaData.TenantID + `"}},{"match":{"entityType.keyword":"openAIProjectCost"}},{"match":{"entityId.keyword":"` + projID + `"}},{"match":{"deleted":"false"}}]}}}`
			aiRscDocs, err := elastic.ExecuteSearchQuery([]string{elastic.AI_RESOURCES_INDEX}, projCostQuery)
			if err != nil {
				logger.Print(logger.ERROR, "Got error fetching project", []string{aiMetaData.TenantID}, err)
				return
			}

			totalProjectCost := 0.0

			if len(aiRscDocs) > 0 {

				for _, aiRscDoc := range aiRscDocs {

					if entityJsonStr, ok := aiRscDoc["entityJson"].(string); ok {

						var costSruct GroupedCost
						json.Unmarshal([]byte(entityJsonStr), &costSruct)

						if costSruct.Amount.Value > 0.0 {
							totalProjectCost += costSruct.Amount.Value
						}
					}
				}
			}

			// update project cost in json
			aiResourcesQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + aiMetaData.TenantID + `"}},{"match":{"entityType.keyword":"openAIProject"}},{"match":{"entityId.keyword":"` + projID + `"}}]}}}`
			aiRscDocs, err = elastic.ExecuteSearchQuery([]string{elastic.AI_RESOURCES_INDEX}, aiResourcesQuery)
			if err != nil {
				logger.Print(logger.ERROR, "Got error fetching project", []string{aiMetaData.TenantID}, err)
				return
			}

			if len(aiRscDocs) > 0 {

				for _, aiRscDoc := range aiRscDocs {

					if id, ok := aiRscDoc["id"].(string); ok {

						if entityJsonStr, ok := aiRscDoc["entityJson"].(string); ok {

							entityJson := make(map[string]any)
							if err := json.Unmarshal([]byte(entityJsonStr), &entityJson); err != nil {
								logger.Print(logger.ERROR, "Error unmarshalling project ai resource json", []string{aiMetaData.TenantID}, err)
								continue
							}

							entityJson["monthly_cost"] = "$ " + strconv.FormatFloat(totalProjectCost, 'f', 2, 64)

							entityJsonBytes, err := json.Marshal(entityJson)
							if err != nil {
								logger.Print(logger.ERROR, "Error marshalling updated entityJson", []string{aiMetaData.TenantID}, err)
								continue
							}

							updateMeta := map[string]map[string]string{"update": {"_id": id}}
							updateDoc := map[string]map[string]string{"doc": {"entityJson": string(entityJsonBytes)}}

							metaLine, err := json.Marshal(updateMeta)
							if err != nil {
								logger.Print(logger.ERROR, "Error marshalling update metadata", []string{aiMetaData.TenantID}, err)
								continue
							}

							docLine, err := json.Marshal(updateDoc)
							if err != nil {
								logger.Print(logger.ERROR, "Error marshalling doc update", []string{aiMetaData.TenantID}, err)
								continue
							}

							bulkCloudResourceRequest := string(metaLine) + "\n" + string(docLine) + "\n"

							if err := elastic.BulkDocumentsAPI(aiMetaData.TenantID, elastic.AI_RESOURCES_INDEX, bulkCloudResourceRequest); err != nil {
								logger.Print(logger.ERROR, "Error in update of AI resources project doc", []string{aiMetaData.TenantID}, err, bulkCloudResourceRequest)
								return
							}
						}
					}
				}
			}
		}
	}
}

// Not being used today
// func calculateOrgCost(aiMetaData *OpenAIMetaData) {
// 	orgQuery := `{"query":{"bool":{"must":[{"wildcard":{"tenantId.keyword":"` + aiMetaData.TenantID + `"}},{"match":{"entityType.keyword":"openAIOrg"}},{"match":{"deleted":"false"}}]}}}`
// 	orgDocs, err := elastic.ExecuteSearchQuery([]string{elastic.AI_RESOURCES_INDEX}, orgQuery)
// 	if err != nil {
// 		logger.Print(logger.ERROR, "Got error fetching project", []string{aiMetaData.TenantID}, err)
// 		return
// 	}

// 	for _, orgDoc := range orgDocs {
// 		if orgID, ok := orgDoc["entityId"].(string); ok {

// 			// accumulate org cost
// 			orgCostQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + aiMetaData.TenantID + `"}},{"match":{"entityType.keyword":"openAIOrgCost"}},{"match":{"entityId.keyword":"` + orgID + `"}},{"match":{"deleted":"false"}}]}}}`
// 			aiRscDocs, err := elastic.ExecuteSearchQuery([]string{elastic.AI_RESOURCES_INDEX}, orgCostQuery)
// 			if err != nil {
// 				logger.Print(logger.ERROR, "Got error fetching project", []string{aiMetaData.TenantID}, err)
// 				return
// 			}

// 			totalOrgCost := 0.0

// 			if len(aiRscDocs) > 0 {

// 				for _, aiRscDoc := range aiRscDocs {

// 					if entityJsonStr, ok := aiRscDoc["entityJson"].(string); ok {

// 						var costSruct GroupedCost
// 						json.Unmarshal([]byte(entityJsonStr), &costSruct)

// 						if costSruct.Amount.Value > 0.0 {
// 							totalOrgCost += costSruct.Amount.Value
// 						}
// 					}
// 				}
// 			}

// 			// fetch all project costs and update the org cost with only the difference
// 			projCostQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + aiMetaData.TenantID + `"}},{"match":{"entityType.keyword":"` + common.OPENAI_PROJECT_RESOURCE_TYPE + `"}},{"match":{"deleted":"false"}}]}}}`
// 			aiRscDocs, err = elastic.ExecuteSearchQuery([]string{elastic.AI_RESOURCES_INDEX}, projCostQuery)
// 			if err != nil {
// 				logger.Print(logger.ERROR, "Got error fetching project", []string{aiMetaData.TenantID}, err)
// 				return
// 			}

// 			totalProjectCost := 0.0
// 			for _, aiRscDoc := range aiRscDocs {

// 				if entityJsonStr, ok := aiRscDoc["entityJson"].(string); ok {

// 					entityJSON := make(map[string]any)
// 					if err := json.Unmarshal([]byte(entityJsonStr), &entityJSON); err != nil {
// 						continue
// 					}

// 					if cost, ok := entityJSON["monthly_cost"].(string); ok {
// 						if costValue, err := strconv.ParseFloat(cost[2:], 64); err == nil {
// 							totalProjectCost += costValue
// 						}
// 					}
// 				}
// 			}

// 			totalOrgCost -= totalProjectCost
// 			if totalOrgCost < 0 {
// 				totalOrgCost = 0.0
// 			}

// 			// update org cost in json
// 			aiResourcesQuery := `{"query":{"bool":{"must":[{"wildcard":{"tenantId.keyword":"` + aiMetaData.TenantID + `"}},{"match":{"entityType.keyword":"openAIOrg"}},{"match":{"entityId.keyword":"` + orgID + `"}}]}}}`
// 			aiRscDocs, err = elastic.ExecuteSearchQuery([]string{elastic.AI_RESOURCES_INDEX}, aiResourcesQuery)
// 			if err != nil {
// 				logger.Print(logger.ERROR, "Got error fetching project", []string{aiMetaData.TenantID}, err)
// 				return
// 			}

// 			if len(aiRscDocs) > 0 {

// 				for _, aiRscDoc := range aiRscDocs {

// 					if id, ok := aiRscDoc["id"].(string); ok {

// 						if entityJsonStr, ok := aiRscDoc["entityJson"].(string); ok {

// 							entityJson := make(map[string]any)
// 							if err := json.Unmarshal([]byte(entityJsonStr), &entityJson); err != nil {
// 								logger.Print(logger.ERROR, "Error unmarshalling project ai resource json", []string{aiMetaData.TenantID}, err)
// 								continue
// 							}

// 							entityJson["monthly_cost"] = "$ " + strconv.FormatFloat(totalOrgCost, 'f', 2, 64)

// 							entityJsonBytes, err := json.Marshal(entityJson)
// 							if err != nil {
// 								logger.Print(logger.ERROR, "Error marshalling updated entityJson", []string{aiMetaData.TenantID}, err)
// 								continue
// 							}

// 							updateMeta := map[string]map[string]string{"update": {"_id": id}}
// 							updateDoc := map[string]map[string]string{"doc": {"entityJson": string(entityJsonBytes)}}

// 							metaLine, err := json.Marshal(updateMeta)
// 							if err != nil {
// 								logger.Print(logger.ERROR, "Error marshalling update metadata", []string{aiMetaData.TenantID}, err)
// 								continue
// 							}

// 							docLine, err := json.Marshal(updateDoc)
// 							if err != nil {
// 								logger.Print(logger.ERROR, "Error marshalling doc update", []string{aiMetaData.TenantID}, err)
// 								continue
// 							}

// 							bulkCloudResourceRequest := string(metaLine) + "\n" + string(docLine) + "\n"

// 							if err := elastic.BulkDocumentsAPI(aiMetaData.TenantID, elastic.AI_RESOURCES_INDEX, bulkCloudResourceRequest); err != nil {
// 								logger.Print(logger.ERROR, "Error in update of AI resources project doc", []string{aiMetaData.TenantID}, err, bulkCloudResourceRequest)
// 								return
// 							}
// 						}
// 					}
// 				}
// 			}
// 		}
// 	}
// }
