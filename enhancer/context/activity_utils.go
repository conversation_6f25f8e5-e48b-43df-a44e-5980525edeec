package context

import "regexp"

var ignoreCreateEvents = map[string]struct{}{
	"CreateAccessPolicy":          {},
	"CreateActivation":            {},
	"CreateAlias":                 {},
	"CreateAssociation":           {},
	"CreateBackupPlan":            {},
	"CreateBackupSelection":       {},
	"CreateComplianceItems":       {},
	"CreateConfigRule":            {},
	"CreateEvaluations":           {},
	"CreateExportTask":            {},
	"CreateFilter":                {},
	"CreateFlowLogs":              {},
	"CreateGrant":                 {},
	"CreateHealthCheck":           {},
	"CreateImageRecipe":           {},
	"CreateInvalidation":          {},
	"CreateLabelingJob":           {},
	"CreateLifecyclePolicy":       {},
	"CreateModelCustomizationJob": {},
	"CreateOpsItem":               {},
	"CreateOpsMetadata":           {},
	"CreatePermission":            {},
	"CreatePolicyVersion":         {},
	"CreateProcessingJob":         {},
	"CreateRetentionPolicy":       {},
	"CreateScalingPolicy":         {},
	"CreateScan":                  {},
	"CreateSchedule":              {},
	"CreateSession":               {},
	"CreateTags":                  {},
	"CreateNetworkAcl":            {},
	"CreateAccessKey":             {},
	"CreateTrainingJob":           {},
	"Microsoft.Authorization/denyAssignments/write":                    {},
	"Microsoft.Authorization/locks/write":                              {},
	"Microsoft.Authorization/policyAssignments/write":                  {},
	"Microsoft.Authorization/policyDefinitions/write":                  {},
	"Microsoft.Authorization/policyExemptions/write":                   {},
	"Microsoft.Authorization/roleAssignments/write":                    {},
	"Microsoft.Authorization/roleDefinitions/write":                    {},
	"Microsoft.GuestConfiguration/guestConfigurationAssignments/write": {},
	"Microsoft.Insights/actiongroups/write":                            {},
	"Microsoft.Insights/activityLogAlerts/write":                       {},
	"Microsoft.Insights/dataCollectionRuleAssociations/write":          {},
	"Microsoft.Insights/diagnosticSettings/write":                      {},
	"Microsoft.Insights/metricAlerts/write":                            {},
	"Microsoft.Insights/scheduledqueryrules/write":                     {},
	"Microsoft.Maintenance/configurationAssignments/write":             {},
	"Microsoft.OperationsManagement/solutions/write":                   {},
	"Microsoft.RecoveryServices/vaults/backupPolicies/write":           {},
	"Microsoft.Resources/links/write":                                  {},
	"Microsoft.Resources/tags/write":                                   {},
	"Microsoft.Security/* events":                                      {},
	"PutAlternateContact":                                              {},
	"PutBackupPolicy":                                                  {},
	"PutBackupVaultAccessPolicy":                                       {},
	"PutBucketAcl":                                                     {},
	"PutBucketCors":                                                    {},
	"PutBucketEncryption":                                              {},
	"PutBucketInventoryConfiguration":                                  {},
	"PutBucketLifecycle":                                               {},
	"PutBucketLogging":                                                 {},
	"PutBucketNotification":                                            {},
	"PutBucketOwnershipControls":                                       {},
	"PutBucketPolicy":                                                  {},
	"PutBucketPublicAccessBlock":                                       {},
	"PutBucketTagging":                                                 {},
	"PutBucketVersioning":                                              {},
	"PutComplianceItems":                                               {},
	"PutConfigRule":                                                    {},
	"PutConfigurationRecorder":                                         {},
	"PutConfigurePackageResult":                                        {},
	"PutContactInformation":                                            {},
	"PutCredentials":                                                   {},
	"PutDashboard":                                                     {},
	"PutDeliveryChannel":                                               {},
	"PutEvaluations":                                                   {},
	"PutEventSelectors":                                                {},
	"PutFoundationModelEntitlement":                                    {},
	"PutFunctionEventInvokeConfig":                                     {},
	"PutGatewayResponse":                                               {},
	"PutGroupPolicy":                                                   {},
	"PutInsightSelectors":                                              {},
	"PutIntegration":                                                   {},
	"PutIntegrationResponse":                                           {},
	"PutInventory":                                                     {},
	"PutKeyPolicy":                                                     {},
	"PutLifecycleConfiguration":                                        {},
	"PutLifecycleHook":                                                 {},
	"PutMethod":                                                        {},
	"PutMethodResponse":                                                {},
	"PutMetricAlarm":                                                   {},
	"PutNotificationConfiguration":                                     {},
	"PutParameter":                                                     {},
	"PutRemediationConfigurations":                                     {},
	"PutResourcePolicy":                                                {},
	"PutRetentionPolicy":                                               {},
	"PutRolePolicy":                                                    {},
	"PutRule":                                                          {},
	"PutScalingPolicy":                                                 {},
	"PutSecretValue":                                                   {},
	"PutSubscriptionFilter":                                            {},
	"PutTargets":                                                       {},
	"PutUserPolicy":                                                    {},
	"com.coreos.monitoring.v1.podmonitors.create":                      {},
	"com.coreos.monitoring.v1.prometheusrules.create":                  {},
	"com.coreos.monitoring.v1.servicemonitors.create":                  {},
	"dns.changes.create":                                               {},
	"dns.policies.create":                                              {},
	"dns.resourceRecordSets.create":                                    {},
	"dns.responsePolicies.create":                                      {},
	"dns.responsePolicyRules.create":                                   {},
	"google.admin.AdminService.*":                                      {},
	"io.cert-manager.v1.certificates.create":                           {},
	"io.cert-manager.v1.issuers.create":                                {},
	"io.external-secrets.v1beta1.externalsecrets.create":               {},
	"sh.gatekeeper.templates.v1.constrainttemplates.create":            {},
	"v1.compute.disks.createSnapshot":                                  {},
}

func isCreateEvent(eventName string) bool {

	if _, ok := ignoreCreateEvents[eventName]; ok {
		return false
	}

	patterns := []string{
		`(?i)^Create.*`,               // Create*
		`(?i)^Put.*`,                  // Put*
		`(?i).*insert.*`,              // *insert*
		`(?i)^RunInstances$`,          // exact match
		`(?i).*write$`,                // *write
		`(?i).*create.*`,              // *create*
		`(?i).*beginCreateOrUpdate.*`, // *beginCreateOrUpdate*
	}

	for _, pattern := range patterns {
		matched, err := regexp.MatchString(pattern, eventName)
		if err != nil {
			continue
		}
		if matched {
			return true
		}
	}
	return false
}
