package context

import (
	"encoding/json"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type JiraResource struct {
	ID                string   `json:"id"`
	IssueDocID        string   `json:"issueDocId"`
	TenantID          string   `json:"tenantId"`
	AccountID         string   `json:"accountId"`
	InsertTime        string   `json:"insertTime"`
	Deleted           bool     `json:"deleted"`
	Assignee          string   `json:"assignee"`
	EntityType        string   `json:"entityType"`
	Region            string   `json:"region"`
	ServiceID         string   `json:"serviceId"`
	EntityID          string   `json:"entityId"`
	Environment       string   `json:"environment"`
	Deployment        []string `json:"deployment"`
	OSType            string   `json:"osType"`
	DataSensitivity   []string `json:"dataSensitivity"`
	Applications      []string `json:"applications"`
	Software          []string `json:"softwareComponents"`
	AdditionalDetails string   `json:"additionalDetails"`
}

type JiraTicketContext struct {
	Resources []JiraResource `json:"Resources"`
}

type JiraContextBucket struct {
	Key        string `json:"key"`
	ByResource struct {
		Hits struct {
			Hits []struct {
				Source JiraResource `json:"_source"`
			} `json:"hits"`
		} `json:"hits"`
	} `json:"resource_details"`
}

func GetJiraContext(resourceContext *ResourceContext) {

	logger.Print(logger.INFO, "Processing started for jira context", []string{resourceContext.TenantID})

	aggregatedJiraIssuesQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}}]}},"from":0,"size":0,"aggs":{"byAccount":{"terms":{"field":"accountId.keyword","size":10000,"order":[{"_count":"desc"},{"_key":"asc"}]},"aggs":{"byResourceType":{"terms":{"field":"resourceType.keyword","size":1000,"order":[{"_count":"desc"},{"_key":"asc"}]},"aggs":{"byAssignee":{"terms":{"field":"assignee.keyword","size":1000,"order":[{"_count":"desc"},{"_key":"asc"}]}}}}}}}}`
	jiraIssueAggregation, err := elastic.ExecuteSearchForAggregation([]string{elastic.JIRA_RESOURCES_INDEX}, aggregatedJiraIssuesQuery)
	if err != nil {
		return
	}

	if accountAggregation, ok := jiraIssueAggregation["byAccount"].(map[string]any); ok {

		if accountBuckets, ok := accountAggregation["buckets"].([]any); ok {

			for _, accountBucket := range accountBuckets {

				if accountBucketMap, ok := accountBucket.(map[string]any); ok {

					if account, ok := accountBucketMap["key"].(string); ok {

						if resourceTypeAggregation, ok := accountBucketMap["byResourceType"].(map[string]any); ok {

							if resourceTypeBuckets, ok := resourceTypeAggregation["buckets"].([]any); ok {

								for _, resourceTypeBucket := range resourceTypeBuckets {

									if resourceTypeBucketMap, ok := resourceTypeBucket.(map[string]any); ok {

										if resourceType, ok := resourceTypeBucketMap["key"].(string); ok {

											if assigneeAggregation, ok := resourceTypeBucketMap["byAssignee"].(map[string]any); ok {

												if assigneeBuckets, ok := assigneeAggregation["buckets"].([]any); ok {

													for _, assigneeBucket := range assigneeBuckets {
														if assigneeBucketMap, ok := assigneeBucket.(map[string]any); ok {
															if assignee, ok := assigneeBucketMap["key"].(string); ok {
																ownersByType, exists := resourceContext.GetJiraOpsOwners(account)
																if !exists {
																	ownersByType = make(map[string][]string)
																}

																if _, ok := ownersByType[resourceType]; !ok {
																	ownersByType[resourceType] = []string{}
																}

																ownersByType[resourceType] = append(
																	ownersByType[resourceType],
																	assignee,
																)

																// Store the updated map back
																resourceContext.SetJiraOpsOwners(account, ownersByType)
															}
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	logger.Print(logger.INFO, "Processing complete for jira context", []string{resourceContext.TenantID})
}

func getJiraContextOfResource(resourceContext *ResourceContext, collectedDocIDs []string) (err error) {

	if enabled, ok := resourceContext.GetEnabledService("jira"); !ok || !enabled {
		return
	}

	var (
		resourceIDToDocMap = make(map[string][]string)
		resourceIDs        []string
		resourceNames      []string
	)

	for _, resourceDocID := range collectedDocIDs {
		if rctxInstDoc, ok := resourceContext.GetResourceContextInsertDoc(resourceDocID); ok {
			resourceID := rctxInstDoc.ResourceID
			resourceIDToDocMap[resourceID] = append(resourceIDToDocMap[resourceID], resourceDocID)
			resourceIDs = append(resourceIDs, resourceID)

			resourceName := rctxInstDoc.ResourceName

			// Some resource names have escapable characters
			resourceName = strings.ReplaceAll(resourceName, `\`, `\\`)
			resourceName = strings.ReplaceAll(resourceName, `"`, `\"`)

			resourceNames = append(resourceNames, resourceName)
		}
	}

	aggregatedJiraQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"deleted":"false"}},{"terms":{"entityId.keyword":["` + strings.Join(resourceIDs, `","`) + `"]}},{"terms":{"entityId.keyword":["` + strings.Join(resourceNames, `","`) + `"]}}]}},"size":0,"aggs":{"byResource":{"terms":{"field":"entityId.keyword","size":100},"aggs":{"resource_details":{"top_hits":{"size":1,"sort":[{"insertTime":{"order":"desc"}}],"_source":{"includes":["id","issueDocId","tenantId","insertTime","deleted","assignee","entityType","region","entityId","environment","deployment","osType","dataSensitivity","applications","additionalDetails"]}}}}}}}`
	jiraAggregation, err := elastic.ExecuteSearchForAggregation([]string{elastic.JIRA_RESOURCES_INDEX}, aggregatedJiraQuery)
	if err != nil {
		return
	}

	if resourceAggregation, ok := jiraAggregation["byResource"].(map[string]any); ok {

		jsonData, err := json.Marshal(resourceAggregation)
		if err != nil {
			return err
		}

		var resourceAgg struct {
			Buckets []JiraContextBucket `json:"buckets"`
		}
		if err := json.Unmarshal(jsonData, &resourceAgg); err != nil {
			return err
		}

		for _, resourceBucket := range resourceAgg.Buckets {
			for _, jiraRscBucket := range resourceBucket.ByResource.Hits.Hits {
				for _, resourceDocID := range resourceIDToDocMap[resourceBucket.Key] {

					if resourceContextInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(resourceDocID); ok {

						processJiraResourceBucket(&resourceContextInsertDoc, jiraRscBucket.Source, resourceContext)

						resourceContext.SetResourceContextInsertDoc(resourceDocID, resourceContextInsertDoc)
					}
				}
			}
		}
	}
	return
}

func processJiraResourceBucket(resourceContextInsertDoc *common.ResourceContextInsertDoc, jiraResource JiraResource, resourceContext *ResourceContext) {

	if len(jiraResource.Assignee) > 0 {
		resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
			resourceContext.GetUserContextItem(jiraResource.Assignee, common.JIRA_USER_TYPE, "User is assigned a jira ticket related to this resource", "", nil),
		)
	}

	if len(jiraResource.Environment) > 0 {
		resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
			common.ResourceContextItem{
				Name: jiraResource.Environment,
				Type: common.JIRA_ENV_TYPE,
			},
		)
	}

	if len(jiraResource.Deployment) > 0 {
		for _, deployment := range jiraResource.Deployment {
			resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment = append(
				resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment,
				common.ResourceContextItem{
					Name: deployment,
					Type: common.JIRA_DEPLOYMENT_TYPE,
				},
			)
		}
	}

	if len(jiraResource.OSType) > 0 {
		resourceContextInsertDoc.OSType = jiraResource.OSType
	}

	if len(jiraResource.DataSensitivity) > 0 {
		for _, sensitity := range jiraResource.DataSensitivity {
			resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity = append(resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity,
				common.ResourceContextItem{
					Name: sensitity,
					Type: common.JIRACONTEXT_SENSITIVITY_TYPE,
				},
			)
		}
	}

	if len(jiraResource.Software) > 0 {
		for _, software := range jiraResource.Software {
			resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = append(
				resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware,
				common.ResourceContextItem{
					Name: software,
					Type: common.JIRA_SOFTWARE_TYPE,
				},
			)
		}
	}

	if len(jiraResource.Applications) > 0 {
		for _, application := range jiraResource.Applications {
			resourceContextInsertDoc.ResourceAppTypes.DerivedApp = append(
				resourceContextInsertDoc.ResourceAppTypes.DerivedApp,
				common.ResourceContextItem{
					Name: application,
					Type: common.JIRA_APP_TYPE,
				},
			)
		}
	}
}
