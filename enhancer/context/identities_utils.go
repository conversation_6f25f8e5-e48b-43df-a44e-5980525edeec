package context

import (
	"encoding/json"
	"regexp"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

var sddlSyntax = map[string]string{
	"AO":    "Account operators",
	"RU":    "Alias to allow previous Windows 2000",
	"AN":    "Anonymous logon",
	"AU":    "Authenticated users",
	"BA":    "Built-in administrators",
	"BG":    "Built-in guests",
	"BO":    "Backup operators",
	"BU":    "Built-in users",
	"CA":    "Certificate server administrators",
	"CG":    "Creator group",
	"CO":    "Creator owner",
	"DA":    "Domain administrators",
	"DC":    "Domain computers",
	"DD":    "Domain controllers",
	"DG":    "Domain guests",
	"DU":    "Domain users",
	"EA":    "Enterprise administrators",
	"WD":    "Everyone",
	"PA":    "Group Policy administrators",
	"IU":    "Interactively logged-on user",
	"LA":    "Local administrator",
	"LG":    "Local guest",
	"LS":    "Local service account",
	"SY":    "Local system",
	"NU":    "Network logon user",
	"NO":    "Network configuration operators",
	"NS":    "Network service account",
	"PO":    "Printer operators",
	"PS":    "Personal self",
	"PU":    "Power users",
	"RS":    "RAS servers group",
	"RD":    "Terminal server users",
	"RE":    "Replicator",
	"RC":    "Restricted code",
	"SA":    "Schema administrators",
	"SO":    "Server operators",
	"SU":    "Service logon user",
	"LWW":   "Lippincott Williams & Wilkins",
	"GA":    "General Availability",
	"WA":    "",
	"SVC":   "",
	"Admin": "",
	"K8s":   "Kubernetes",
	"C":     "",
	"A":     "Admin",
	"CW":    "Contract Worker",
}

var sddlEmail = map[string]string{
	"AO":  "Account operators",
	"RU":  "Alias to allow previous Windows 2000",
	"AN":  "Anonymous logon",
	"AU":  "Authenticated users",
	"BA":  "Built-in administrators",
	"BG":  "Built-in guests",
	"BO":  "Backup operators",
	"BU":  "Built-in users",
	"CA":  "Certificate server administrators",
	"CG":  "Creator group",
	"CO":  "Creator owner",
	"DA":  "Domain administrators",
	"DC":  "Domain computers",
	"DD":  "Domain controllers",
	"DG":  "Domain guests",
	"DU":  "Domain users",
	"EA":  "Enterprise administrators",
	"WD":  "Everyone",
	"PA":  "Group Policy administrators",
	"IU":  "Interactively logged-on user",
	"LA":  "Local administrator",
	"LG":  "Local guest",
	"LS":  "Local service account",
	"SY":  "Local system",
	"NU":  "Network logon user",
	"NO":  "Network configuration operators",
	"NS":  "Network service account",
	"PO":  "Printer operators",
	"PS":  "Personal self",
	"PU":  "Power users",
	"RS":  "RAS servers group",
	"RD":  "Terminal server users",
	"RE":  "Replicator",
	"RC":  "Restricted code",
	"SA":  "Schema administrators",
	"SO":  "Server operators",
	"SU":  "Service logon user",
	"LWW": "Lippincott Williams & Wilkins",
	"GA":  "General Availability",
	"WA":  "",
	"SVC": "",
	"A":   "Admin",
	"K":   "Kubernetes",
	"C":   "",
	"CW":  "Contract Worker",
}

func isServiceIdentityType(identityType string, identitiesDoc common.IdentitiesDoc) bool {

	// deleted and non-human precize created identities should not be considered for evaluation so return as service identity
	if isPrecizeCreatedIdentity(identityType) {
		if identitiesDoc.Deleted || identitiesDoc.IdentityStatus == common.VALID_IDENTITY_STATUS || identitiesDoc.IdentityStatus == common.AMBIGUOUS_IDENTITY_STATUS {
			return true
		}
	}

	return identityType == common.AWS_IAM_ROLE_IDENTITY_TYPE ||
		identityType == common.TRUSTED_ENTITY_IDENTITY_TYPE ||
		identityType == common.GRAPH_APPLICATION_IDENTITY_TYPE ||
		identityType == common.APP_REGISTRATION_IDENTITY_TYPE ||
		identityType == common.SERVICE_ACCOUNT_IDENTITY_TYPE ||
		identityType == common.SA_POLICY_BINDING_IDENTITY_TYPE ||
		identityType == common.OPEN_AI_SERVICE_ACCOUNT_IDENTITY_TYPE ||
		identityType == common.TAGGED_USER_TYPE ||
		identityType == common.DEFINED_USER_TYPE ||
		identityType == common.ACTIVITY_USER_IDENTITY_TYPE ||
		identityType == common.DERIVED_USER_TYPE ||
		identityType == common.GITHUB_USER_TYPE ||
		identityType == common.GITLAB_USER_TYPE ||
		identityType == common.BITBUCKET_USER_TYPE
}

func isPrecizeCreatedIdentity(identityType string) bool {
	return identityType == common.TAGGED_IDENTITY_TYPE || identityType == common.DEFINED_IDENTITY_TYPE ||
		identityType == common.ACTIVITY_IDENTITY_TYPE || identityType == common.DERIVED_IDENTITY_TYPE ||
		identityType == common.GITHUB_IDENTITY_TYPE || identityType == common.GITLAB_IDENTITY_TYPE ||
		identityType == common.BITBUCKET_IDENTITY_TYPE
}

func determineUserStatus(identitiesDoc common.IdentitiesDoc) (bool, bool) {

	userSource := getPrimaryUserSource(identitiesDoc.TenantID)

	var deleted, cloudUser bool

	if identitiesDoc.Type == common.AZURE_AD_USER_IDENTITY_TYPE {

		if userSource == "adUser" {
			deleted = identitiesDoc.Deleted
		}

		cloudUser = hasCloudRoles(identitiesDoc.AdditionalInfo)
	} else {
		cloudUser = true
	}

	return deleted, cloudUser
}

func hasCloudRoles(priorityConfigsStr string) bool {

	priorityConfigs := make(map[string]any)
	err := json.Unmarshal([]byte(priorityConfigsStr), &priorityConfigs)
	if err != nil {
		return false
	}

	if roleAssignments, ok := priorityConfigs["roleAssignments"].([]any); ok && len(roleAssignments) > 0 {
		return true
	}

	if assignedRoles, ok := priorityConfigs["assignedRoles"].([]any); ok && len(assignedRoles) > 0 {
		return true
	}

	return false
}

func handleIdentitiesWithSDDL(identityID, identityName *string,
	resourceContext *ResourceContext) (isSddl bool) {

	if !resourceContext.SDDLStatus {
		return
	}

	uncleanedEmail := *identityID

	derivedEmailWithoutSddl := removeSddlSyntaxFromEmail(*identityID)

	if doesNameHaveSddlSyntax(*identityName) || (derivedEmailWithoutSddl != *identityID) {
		isSddl = true
		if _, err := common.ParseAddress(*identityName); err == nil {
			*identityName = common.GetFormattedNameFromEmail(*identityName)
		} else {
			// First Remove SDDL from email
			*identityName = removeSddlFromName(*identityName)
			if _, err = common.ParseAddress(derivedEmailWithoutSddl); err == nil {

				isValidEmail := false
				ok := false

				if isValidEmail, ok = resourceContext.GetEmailStatus(derivedEmailWithoutSddl); !ok {
					emailNameMap := map[string]string{
						derivedEmailWithoutSddl: *identityName,
					}
					if emailStatusMap, err := checkEmailValidity(emailNameMap, true, resourceContext); err == nil {
						if isValidEmail, ok = emailStatusMap[derivedEmailWithoutSddl]; ok {
							resourceContext.SetEmailStatus(derivedEmailWithoutSddl, isValidEmail)
							if addr, err := common.ParseAddress(derivedEmailWithoutSddl); err == nil {
								resourceContext.SetEmailStatus(derivedEmailWithoutSddl, isValidEmail)
								*identityID = addr.Address
								resourceContext.SetChildPrimaryEmail(uncleanedEmail, *identityID)
							}
						}
					}
				} else {
					// IdentityId already present in emailStatusCache
					resourceContext.SetEmailStatus(derivedEmailWithoutSddl, isValidEmail)
					*identityID = derivedEmailWithoutSddl
					resourceContext.SetChildPrimaryEmail(uncleanedEmail, *identityID)
				}

				if !ok {
					//Remove sddl from name
					derivedEmail := deriveEmailFromName(*identityName, resourceContext)
					if addr, err := common.ParseAddress(derivedEmail); err == nil {
						*identityID = addr.Address
						resourceContext.SetChildPrimaryEmail(uncleanedEmail, *identityID)
					}
				}
			}
		}
	}

	return isSddl
}

func handleUserContextWithSDDL(email, name *string, r *ResourceContext) (isSddl bool) {

	if !r.SDDLStatus {
		return
	}

	derivedEmailWithoutSddl := removeSddlSyntaxFromEmail(*email)

	if derivedEmailWithoutSddl != *email {
		isSddl = true
		//Remove sddl from email
		if _, err := common.ParseAddress(derivedEmailWithoutSddl); err == nil {

			if isValidEmail, ok := r.GetEmailStatus(derivedEmailWithoutSddl); !ok {
				emailNameMap := map[string]string{
					derivedEmailWithoutSddl: *name,
				}

				if emailStatusMap, err := checkEmailValidity(emailNameMap, true, r); err == nil {
					if isValidEmail, ok = emailStatusMap[derivedEmailWithoutSddl]; ok {
						r.SetEmailStatus(derivedEmailWithoutSddl, isValidEmail)
						r.SetChildPrimaryEmail(*email, derivedEmailWithoutSddl)
						*email = derivedEmailWithoutSddl
					}
				}

				if !isValidEmail {
					if len(*name) <= 0 {
						// no name passed

						*name = common.GetFormattedNameFromEmail(derivedEmailWithoutSddl)
					}
					// Remove Sddl from name
					emailFromName := deriveEmailFromName(*name, r)
					if addr, err := common.ParseAddress(emailFromName); err == nil {
						r.SetChildPrimaryEmail(*email, addr.Address)
						*email = addr.Address
					}
				}
			} else {
				if ok {
					r.SetChildPrimaryEmail(*email, derivedEmailWithoutSddl)
					*email = derivedEmailWithoutSddl
				}
			}
		}
	}

	return
}

func regexToRemoveSddlSyntaxFromName(name string) string {
	modifiedName := name

	regexSpecialChars := regexp.MustCompile(`[^a-zA-Z0-9]+`)
	modifiedName = regexSpecialChars.ReplaceAllString(modifiedName, " ")
	modifiedName = strings.TrimSpace(modifiedName)
	nameAfterRemovingSpecialChars := modifiedName

	regexPattern := `\b(` + strings.Join(sddlKeywords(), "|") + `)\b`

	regex := regexp.MustCompile(regexPattern)
	modifiedName = regex.ReplaceAllString(modifiedName, "")

	modifiedName = strings.TrimSpace(modifiedName)

	// if name contains special chars and no sddl prefixes return original name
	if nameAfterRemovingSpecialChars == modifiedName {
		return name
	}
	return modifiedName
}

func removeSddlFromName(name string) string {
	name = strings.ToLower(name)

	name = regexToRemoveSddlSyntaxFromName(name)
	if doesNameHaveSddlSyntax(name) {
		name = regexToRemoveSddlSyntaxFromName(name)
	}

	return common.ConvertToTitleCase(name)
}

func removeSddlSyntaxFromEmail(email string) string {
	validEmail := strings.Index(email, "@")
	if validEmail == -1 {
		// returning email so that sddl check does not pass later
		return email
	}

	emailPrefix := email[:validEmail]
	emailDomain := email[validEmail:]

	sddlPattern := strings.Join(sddlEmailKeywords(), "|")

	prefixRegex := regexp.MustCompile(`(?i)^(` + sddlPattern + `)[._+\-]`)
	suffixRegex := regexp.MustCompile(`(?i)[._+\-](` + sddlPattern + `)$`)

	emailPrefix = prefixRegex.ReplaceAllString(emailPrefix, "")
	emailPrefix = suffixRegex.ReplaceAllString(emailPrefix, "")

	return emailPrefix + emailDomain
}

func sddlKeywords() []string {
	keywords := make([]string, 0, len(sddlSyntax))
	for key := range sddlSyntax {
		keywords = append(keywords, strings.ToLower(key))
	}
	return keywords
}

func sddlEmailKeywords() []string {
	keywords := make([]string, 0, len(sddlEmail))
	for key := range sddlEmail {
		keywords = append(keywords, strings.ToLower(key))
	}
	return keywords
}

func doesNameHaveSddlSyntax(name string) bool {
	name = strings.Trim(name, " ")
	name = strings.ToLower(name)

	return name != regexToRemoveSddlSyntaxFromName(name)
}

// Not being used
// func syncSDDLUserActiveStatus(userResource *UserContext, resourceContext *ResourceContext) {
// 	if u, ok := resourceContext.GetUserResource(userResource.Email); ok {
// 		userResource.Active = u.Active
// 		userResource.IdentityDocID = u.IdentityDocID
// 	}
// }

func updateEmailToNameMapping(resourceContext *ResourceContext, email, name string) {
	if fullName, ok := resourceContext.GetEmailToFullName(email); ok {
		if len(name) > len(fullName) {
			resourceContext.SetEmailToFullName(email, name)
		}
	} else {
		resourceContext.SetEmailToFullName(email, name)
	}
}

func addEmailToEmailStatusReq(resourceContext *ResourceContext, userResource UserContext, isActive, isSddl bool, userMaps *UserMaps) {
	if _, ok := resourceContext.GetEmailStatus(userResource.Email); !ok {
		if isActive {
			// If not deleted via cloud, add email to check status
			if name, ok := userMaps.emailNameMap[userResource.Email]; !ok || len(userResource.Name) > len(name) {
				userMaps.emailNameMap[userResource.Email] = userResource.Name
			}
		} else {
			// If deleted, set as inactive in cache without calling emailStatus again
			resourceContext.SetEmailStatus(userResource.Email, false)
			resourceContext.SetUpdateUndeliverableValidEmail(userResource.Email)
		}
	} else if !isSddl {
		if !isActive {
			// already present in cache, is not sddl and inacitive
			resourceContext.SetEmailStatus(userResource.Email, false)
			resourceContext.SetUpdateUndeliverableValidEmail(userResource.Email)
		}
	}
}

func processAzureADUser(resourceContext *ResourceContext, userMaps *UserMaps,
	identitiesDoc common.IdentitiesDoc, userResource *UserContext, email string) {

	var existingUsrRsc UserContext
	if u, ok := resourceContext.GetUserResource(email); ok {
		existingUsrRsc = u
	}

	processAzureAdditionalInfo(resourceContext, userMaps, identitiesDoc, userResource, &existingUsrRsc)

	// is sddl flag should be preserved in case of duplicate user resources
	if existingUsrRsc.IsSddl && !userResource.IsSddl {
		userResource.IsSddl = true
	}

	resourceContext.SetUserResource(email, *userResource)
}

func processAzureAdditionalInfo(resourceContext *ResourceContext, userMaps *UserMaps,
	identitiesDoc common.IdentitiesDoc, userResource *UserContext, existingUser *UserContext) {

	additionalInfo := make(map[string]any)
	if err := json.Unmarshal([]byte(identitiesDoc.AdditionalInfo), &additionalInfo); err != nil {
		return
	}

	if userID, ok := additionalInfo["id"].(string); ok {
		resourceContext.SetUserIDToEmail(userID, userResource.Email)

		userResource.Sources = append(userResource.Sources, UserContextSource{
			ResourceID:   userID,
			AccountID:    identitiesDoc.AccountID,
			IdentityType: common.AZURE_ADUSER_RESOURCE_TYPE,
		})
	}

	processManager(additionalInfo, userResource, existingUser)
	processGroups(additionalInfo, userResource, existingUser)
	processDepartment(additionalInfo, userResource, existingUser)
	processJobTitle(userMaps, additionalInfo, userResource, existingUser)

}

func processManager(additionalInfo map[string]any, userResource *UserContext, existingUser *UserContext) {

	if len(existingUser.Manager) > 0 {
		userResource.Manager = existingUser.Manager
	}

	if managerObj, ok := additionalInfo["manager"].(map[string]any); ok {
		if manager, ok := managerObj["id"].(string); ok && len(manager) > 0 {
			userResource.Manager[manager] = ID_TYPE
		}
	}
}

func processGroups(additionalInfo map[string]any, userResource *UserContext, existingUser *UserContext) {

	if len(existingUser.Groups) > 0 {
		userResource.Groups = existingUser.Groups
	}

	if groupsList, ok := additionalInfo["memberGroups"].([]string); ok {
		for _, group := range groupsList {
			userResource.Groups[group] = struct{}{}
		}
	}
}

func processDepartment(additionalInfo map[string]any, userResource *UserContext, existingUser *UserContext) {

	if len(existingUser.Department) > 0 {
		userResource.Department = existingUser.Department
	}

	if department, ok := additionalInfo["department"].(string); ok && len(department) > 0 {

		userResource.Department[department] = struct{}{}

		team := FormatContextValue(department)
		userResource.Team[team] = struct{}{}

		fullValue := strings.ToLower(team)
		if len(fullValue) <= 5 {
			fullValue = `\b` + fullValue + `\b`
		}
		fullValue = strings.ReplaceAll(fullValue, " ", `[-_\s]*`)

		globalValuesMutex.Lock()
		teamValues[team] = append(teamValues[team], fullValue)
		globalValuesMutex.Unlock()
	}
}

func processJobTitle(userMaps *UserMaps, additionalInfo map[string]any, userResource *UserContext, existingUser *UserContext) {

	if len(existingUser.JobTitle) > 0 {
		userResource.JobTitle = existingUser.JobTitle
	}

	if jobTitle, ok := additionalInfo["jobTitle"].(string); ok && len(jobTitle) > 0 {
		userResource.JobTitle[jobTitle] = struct{}{}

		for dept := range userResource.Department {
			for title := range userResource.JobTitle {
				orgMapKey := strings.ToLower(dept + title)
				userMaps.orgMap[orgMapKey] = append(userMaps.orgMap[orgMapKey], userResource.Email)
			}
		}
	}
}

func handleIdentityWithExternalEmail(resourceContext *ResourceContext, temp *UserContext) {

	var (
		isValidEmail, isValidEmailDerived bool
		originalEmail                     = temp.Email
	)

	emailWithoutExt := extractExternalEmail(temp.Email)

	if addr, err := common.ParseAddress(emailWithoutExt); err == nil {

		temp.Email = addr.Address
		temp.IsSddl = handleUserContextWithSDDL(&temp.Email, &temp.Name, resourceContext)

		if isValidEmail, isValidEmailDerived = resourceContext.GetEmailStatus(temp.Email); !isValidEmailDerived {
			emailNameMap := map[string]string{
				temp.Email: temp.Name,
			}

			emailStatusMap, err := checkEmailValidity(emailNameMap, false, resourceContext)
			if err != nil {
				return
			}

			if len(emailStatusMap) > 0 {
				isValidEmail = emailStatusMap[temp.Email]
				resourceContext.SetEmailStatus(temp.Email, isValidEmail)
			}

			if fullName, ok := resourceContext.GetEmailToFullName(temp.Email); ok && len(fullName) > len(temp.Name) {
				temp.Name = fullName
			}

			if len(temp.Name) <= 0 {
				temp.Name = common.GetFormattedNameFromEmail(temp.Email)
			}

		}

		temp.Active = isValidEmail
		resourceContext.SetChildPrimaryEmail(originalEmail, temp.Email)
	}
}

func handlePersonalAndMicrosoftEmail(resourceContext *ResourceContext, temp *UserContext) {

	var (
		matchedEmail  string
		name          string
		originalEmail = temp.Email
	)

	if strings.Contains(temp.Email, LIVE_MICROSOFT_KEYWORD) {
		parts := strings.Split(temp.Email, LIVE_MICROSOFT_KEYWORD)
		if len(parts) > 1 {
			matchedEmail = parts[1]
			matchedEmail = strings.ReplaceAll(matchedEmail, "@", "_")
		}
	} else if strings.Contains(temp.Email, MAIL_MICROSOFT_KEYWORD) {
		parts := strings.Split(temp.Email, MAIL_MICROSOFT_KEYWORD)
		if len(parts) > 1 {
			matchedEmail = parts[1]
			matchedEmail = strings.ReplaceAll(matchedEmail, "@", "_")
		}
	}

	identityQuery := `{"query":{"bool":{"must":[{"match":{"deleted":"false"}},{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"type.keyword":"AD_USER"}},{"wildcard":{"identityId.keyword":"*` + matchedEmail + `*"}}]}},"size":"1","from":0}`
	identityDocs, err := elastic.ExecuteSearchQuery([]string{elastic.IDENTITIES_INDEX}, identityQuery)
	if err != nil {
		return
	}

	for _, identityDoc := range identityDocs {

		var identity common.IdentitiesDoc
		jsonBytes, err := json.Marshal(identityDoc)
		if err != nil {
			return
		}
		err = json.Unmarshal(jsonBytes, &identity)
		if err != nil {
			return
		}
		matchedEmail = identity.IdentityID
		name = identity.Name
	}

	if addr, err := common.ParseAddress(matchedEmail); err == nil {

		var (
			isValidEmail, isValidEmailDerived bool
			emailStatusMap                    map[string]bool
		)

		temp.Email = addr.Address
		temp.Name = name
		temp.IsSddl = handleUserContextWithSDDL(&temp.Email, &name, resourceContext)

		if isValidEmail, isValidEmailDerived = resourceContext.GetEmailStatus(temp.Email); isValidEmailDerived {
			emailNameMap := map[string]string{
				temp.Email: name,
			}

			emailStatusMap, err = checkEmailValidity(emailNameMap, false, resourceContext)
			if err != nil {
				return
			}

			if len(emailStatusMap) > 0 {
				isValidEmail = emailStatusMap[temp.Email]
				resourceContext.SetEmailStatus(temp.Email, isValidEmail)
			}

			if fullName, ok := resourceContext.GetEmailToFullName(temp.Email); ok && len(fullName) > len(temp.Name) {
				temp.Name = fullName
			}

			if len(temp.Name) <= 0 {
				temp.Name = common.GetFormattedNameFromEmail(temp.Email)
			}

		}

		temp.Active = isValidEmail
		resourceContext.SetChildPrimaryEmail(originalEmail, temp.Email)
	}

	return
}

func handleExEmployeeReassignment(resourceContext *ResourceContext, userResource *UserContext, userMaps *UserMaps) {

	for userType := range userResource.Type {

		switch userType {

		case common.AZURE_AD_USER_IDENTITY_TYPE, common.OKTA_USER_IDENTITY_TYPE:

			if len(userResource.JobTitle) > 0 {

				var managerEmails []string
				for managerValue, managerType := range userResource.Manager {
					managerEmail := managerValue
					if managerType == ID_TYPE {
						managerEmail, _ = resourceContext.GetUserIDToEmail(managerValue)
					}

					managerEmails = append(managerEmails, managerEmail)
				}

				var sameJobEmployees []string

				for dept := range userResource.Department {
					for title := range userResource.JobTitle {
						orgMapKey := strings.ToLower(dept + title)
						sameJobEmployees = append(sameJobEmployees, userMaps.orgMap[orgMapKey]...)
					}
				}

				reassignable := common.RemoveDuplicatesFromStringSlice(sameJobEmployees)
				reassigned := findReassignee(resourceContext, userResource, reassignable, "Resource has been reassigned from ex employee "+userResource.Email)

				if len(reassigned.Name) <= 0 && len(managerEmails) > 0 {
					if activeManager, found := findManagerReassignee(resourceContext, managerEmails, userMaps, userResource); found && len(activeManager.Name) > 0 {
						userResource.Reassigned = activeManager
					}
				} else if len(reassigned.Name) > 0 {
					userResource.Reassigned = reassigned
				}
			}
		}
	}
}

func findReassignee(resourceContext *ResourceContext, user *UserContext, reassignableEmails []string, desc string) common.ResourceContextItem {
	var (
		reassigned       common.ResourceContextItem
		currentlyMatched struct {
			groupsMatched  int
			managerMatched int
		}
	)

	if len(reassignableEmails) == 0 {
		return reassigned
	}

	for _, reassignableEmail := range reassignableEmails {
		if possibleUser, ok := resourceContext.GetUserResource(reassignableEmail); ok {

			// not using possibleUser.IsActive since all UserRsc might not have this flag set at this time
			isEmailActive, _ := resourceContext.GetEmailStatus(reassignableEmail)
			if possibleUser.Email == user.Email || !isEmailActive {
				continue
			}

			if len(reassigned.Name) <= 0 {
				// By default, first match
				reassigned = common.ResourceContextItem{
					Name: formCompleteEmailFormat(possibleUser.Name, possibleUser.Email),
					Type: common.REASSIGNED_USER_TYPE,
					Desc: desc,
				}
			}

			var (
				groupsMatched, managerMatched int
				newReassignee                 bool
			)

			// Count matching groups
			for group := range possibleUser.Groups {
				if _, ok := user.Groups[group]; ok {
					groupsMatched++
				}
			}

			// Check manager match
			for manager := range possibleUser.Manager {
				if _, ok := user.Manager[manager]; ok {
					managerMatched++
				}
			}

			// Determine if this is a better match
			if currentlyMatched.groupsMatched == groupsMatched {
				if currentlyMatched.managerMatched > managerMatched {
					continue
				} else {
					newReassignee = true
				}
			} else if currentlyMatched.groupsMatched < groupsMatched {
				newReassignee = true
			}

			if newReassignee {
				reassigned = common.ResourceContextItem{
					Name: formCompleteEmailFormat(possibleUser.Name, possibleUser.Email),
					Type: common.REASSIGNED_USER_TYPE,
					Desc: desc,
				}

				// Update current match metrics
				currentlyMatched.groupsMatched = groupsMatched
				currentlyMatched.managerMatched = managerMatched
			}
		}
	}

	return reassigned
}

func findManagerReassignee(resourceContext *ResourceContext, initialManagerEmails []string, userMaps *UserMaps, userResource *UserContext) (common.ResourceContextItem, bool) {
	if len(initialManagerEmails) <= 0 {
		return common.ResourceContextItem{}, false
	}

	for _, initialManagerEmail := range initialManagerEmails {
		managerUser, ok := resourceContext.GetUserResource(initialManagerEmail)
		if !ok {
			continue
		}

		isEmailActive, _ := resourceContext.GetEmailStatus(initialManagerEmail)
		if managerUser.Active && isEmailActive {
			return common.ResourceContextItem{
				Name: formCompleteEmailFormat(managerUser.Name, managerUser.Email),
				Type: common.REASSIGNED_USER_TYPE,
				Desc: "Resource has been reassigned from ex employee " + userResource.Email,
			}, true
		}
	}

	// Managers are not active, find reassignable user for the manager
	for _, initialManagerEmail := range initialManagerEmails {
		managerUser, ok := resourceContext.GetUserResource(initialManagerEmail)
		if !ok {
			continue
		}

		var sameJobEmployees []string

		for dept := range managerUser.Department {
			for title := range managerUser.JobTitle {
				orgMapKey := strings.ToLower(dept + title)
				sameJobEmployees = append(sameJobEmployees, userMaps.orgMap[orgMapKey]...)
			}
		}

		reassignable := common.RemoveDuplicatesFromStringSlice(sameJobEmployees)
		reassigned := findReassignee(resourceContext, &managerUser, reassignable, "Resource has been reassigned from ex employee "+userResource.Email)

		if len(reassigned.Name) > 0 {
			return reassigned, true
		}

		// No reassignable user found, manager's manager could be a potential reassignee
		for managerManagerValue, managerManagerType := range managerUser.Manager {
			managerManagerEmail := managerManagerValue
			if managerManagerType == ID_TYPE {
				managerManagerEmail, _ = resourceContext.GetUserIDToEmail(managerManagerValue)
			}

			if managerManagerUser, ok := resourceContext.GetUserResource(managerManagerEmail); ok {
				isManagerManagerActive, _ := resourceContext.GetEmailStatus(managerManagerEmail)
				if managerManagerUser.Active && isManagerManagerActive {
					return common.ResourceContextItem{
						Name: formCompleteEmailFormat(managerManagerUser.Name, managerManagerUser.Email),
						Type: common.REASSIGNED_USER_TYPE,
						Desc: "Resource has been reassigned from ex employee " + userResource.Email,
					}, true
				}
			}
		}
	}

	return common.ResourceContextItem{}, false
}

func HandleChildEmail(r *ResourceContext, temp *UserContext) {
	var (
		primaryDomain    string
		primaryDomainMap = make(map[string]struct{})
	)

	if len(r.PrimaryDomains) > 0 {
		primaryDomain = r.PrimaryDomains[0]

		for _, primaryDomain := range r.PrimaryDomains {
			primaryDomainMap[primaryDomain] = struct{}{}
		}
	}

	if !strings.HasSuffix(strings.ToLower(temp.Email), strings.ToLower(primaryDomain)) {
		if _, ok := r.GetChildPrimaryEmail(temp.Email); !ok {
			primaryEmail := DerivePrimaryIdentityFromPartner(temp.Email, temp.Name, r)

			iterations := 0
			for {
				iterations++
				if len(primaryEmail) > 0 && !strings.HasSuffix(strings.ToLower(primaryEmail), strings.ToLower(primaryDomain)) {
					primaryEmail = DerivePrimaryIdentityFromPartner(primaryEmail, temp.Name, r)
				} else {
					break
				}

				if iterations > 10 {
					logger.Print(logger.INFO, "Too many iterations in HandleChildEmail", []string{r.TenantID}, temp.Email, primaryEmail)
					break
				}
			}

			if len(primaryEmail) > 0 && temp.Email != primaryEmail {
				r.SetChildPrimaryEmail(temp.Email, primaryEmail)

				if _, ok := primaryDomainMap[temp.Email]; ok {
					return
				}

				// if primary identity of partner is undeliverable, then partner email will also be undeliverable
				// vice-versa is being handled by platform
				if isPrimaryActive, ok := r.GetEmailStatus(primaryEmail); ok {
					if !isPrimaryActive {
						temp.Active = false
					}
				}
			}
		}
	}
}

func DetermineIdentityStatusForEmail(r *ResourceContext, identityId string, isNonHuman bool) (identityStatus int) {

	if isNonHuman {
		// Non-Human Identity
		if valid, ok := r.GetEmailStatus(identityId); ok {
			if valid {
				identityStatus = common.VALID_IDENTITY_STATUS
			} else {
				identityStatus = common.AMBIGUOUS_IDENTITY_STATUS
			}
		} else {
			identityStatus = common.UNKNOWN_IDENTITY_STATUS
		}
	} else {
		// Human Identity
		if valid, ok := r.GetEmailStatus(identityId); ok {
			if valid {
				identityStatus = common.CURRENT_EMPLOYEE_IDENTITY_STATUS
			} else {
				identityStatus = common.EX_EMPLOYEE_IDENTITY_STATUS
			}
		} else {
			identityStatus = common.UNKNOWN_IDENTITY_STATUS
		}

		if usrRsc, ok := r.GetUserResource(identityId); ok && usrRsc.IsInvalid {
			identityStatus = common.AMBIGUOUS_IDENTITY_STATUS
		}
	}

	return
}

func GetIdentityStatusFromRctx(resourceContextDoc common.ResourceContextInsertDoc, identityID string) (identityStatus int) {
	identityStatus = -1

	nonHumanIdentitySuffixes := []string{
		AWSSERVICE_USER_SUFFIX,
		ACCOUNT_USER_SUFFIX,
		IAM_USER_SUFFIX,
		IAM_ROLE_SUFFIX,
		APP_USER_SUFFIX,
		SERVICEACCOUNT_USER_SUFFIX,
	}

	for _, rctx := range resourceContextDoc.DefinedOwners {
		if strings.ToLower(rctx.IdentityId) == strings.ToLower(identityID) || rctx.IdentityId == "" {
			if common.HasAnySuffix(rctx.Name, nonHumanIdentitySuffixes) {
				identityStatus = common.VALID_IDENTITY_STATUS
			} else {
				identityStatus = common.AMBIGUOUS_IDENTITY_STATUS
			}
		}
	}

	for _, rctx := range resourceContextDoc.CodeOwners {
		if strings.ToLower(rctx.IdentityId) == strings.ToLower(identityID) || rctx.IdentityId == "" {
			if common.HasAnySuffix(rctx.Name, nonHumanIdentitySuffixes) {
				identityStatus = common.VALID_IDENTITY_STATUS
			} else {
				identityStatus = common.AMBIGUOUS_IDENTITY_STATUS
			}
		}
	}

	for _, rctx := range resourceContextDoc.DerivedOwners {
		if strings.ToLower(rctx.IdentityId) == strings.ToLower(identityID) || rctx.IdentityId == "" {
			if common.HasAnySuffix(rctx.Name, nonHumanIdentitySuffixes) {
				identityStatus = common.VALID_IDENTITY_STATUS
			} else {
				identityStatus = common.AMBIGUOUS_IDENTITY_STATUS
			}
		}
	}

	return
}

func extractIdentityNameFromAdditionalInfo(identityName *string, identityType string, additionalInfo map[string]any) {

	name := ""

	switch identityType {
	case common.AZURE_AD_USER_IDENTITY_TYPE:
		firstName, _ := additionalInfo["givenName"].(string)
		lastName, _ := additionalInfo["surname"].(string)

		name = firstName + " " + lastName
		name = strings.TrimSpace(name)
	case common.AWS_SSOUSER_IDENTITY_TYPE:
		user, userOk := additionalInfo["user"].(map[string]any)
		if !userOk {
			break
		}

		nameObj, nameObjOk := user["name"].(map[string]any)
		if !nameObjOk {
			break
		}

		honorificPrefix, _ := nameObj["honorificPrefix"].(string)
		firstName, _ := nameObj["givenName"].(string)
		middleName, _ := nameObj["middleName"].(string)
		lastName, _ := nameObj["familyName"].(string)
		honorificSuffix, _ := nameObj["honorificSuffix"].(string)

		name += honorificPrefix

		if len(firstName) > 0 {
			name += " " + firstName
		}
		if len(middleName) > 0 {
			name += " " + middleName
		}
		if len(lastName) > 0 {
			name += " " + lastName
		}

		name += " " + honorificSuffix
		name = strings.TrimSpace(name)
	}

	if len(name) >= len(*identityName) {
		*identityName = name
	}

}

func InitializeHumanOrNonHumanEvaluation(usrRscKey string, userResource UserContext, resourceContext *ResourceContext, nameList map[string][]string) {

	if userResource.IsUserEvaluated {
		return
	}

	userResource.IsUserEvaluated = true

	if len(userResource.Email) > 0 {
		aiInput := common.GetEmailNameWithoutSpecialCharacters(userResource.Email)
		if userResource.Name != "" {
			aiInput = userResource.Name
		}

		// Set name defined in exceptions index
		if name, ok := resourceContext.GetOwnerEmailName(userResource.Email); ok && len(name) > 0 {
			userResource.Name = name
			aiInput = name
		}

		// if !userResource.CloudUser {
		// 	delete(resourceContext.UserResources, email)
		// }

		HandleChildEmail(resourceContext, &userResource)

		// child email should have the same name as primary email if primary email has a name defined (from customer)
		if primaryEmail, ok := resourceContext.GetChildPrimaryEmail(usrRscKey); ok {
			if name, ok := resourceContext.GetOwnerEmailName(primaryEmail); ok && len(name) > 0 {
				userResource.Name = name
				aiInput = name
			}
		}

		if isAlias, ok := resourceContext.GetAliasUpdate(userResource.Email); ok {
			userResource.IsUser = !isAlias
			aiInput = ""
		} else if primaryEmail, ok := resourceContext.GetChildPrimaryEmail(usrRscKey); ok {
			// If customer has defined the identity status (alias) of parent email use that for child email as well
			if isAlias, ok := resourceContext.GetAliasUpdate(primaryEmail); ok {
				userResource.IsUser = !isAlias
				aiInput = ""
			}
		}

		if len(aiInput) > 0 {
			nameList[aiInput] = append(nameList[aiInput], usrRscKey)
		}
	}

	resourceContext.SetUserResource(usrRscKey, userResource)
}

func EvaluateNamesForHumanOrNonHuman(nameList map[string][]string, resourceContext *ResourceContext) {

	if len(nameList) > 0 {

		exampleNames := map[string]bool{
			"John Doe":    true,
			"Engineering": false,
			"Satyam":      true,
			"DevOps":      false,
		}

		// fetch samples
		PopulateExampleNames(resourceContext.TenantID, exampleNames)
		resp := common.HumanOrNonHumanName(nameList, exampleNames, resourceContext.TenantID)

		for input, hasName := range resp {
			if usrRscKeys, ok := nameList[input]; ok {
				for _, usrRscKey := range usrRscKeys {
					if tmp, ok := resourceContext.GetUserResource(usrRscKey); ok {

						if hasName {
							tmp.IsUser = true
						} else {
							if ok := resourceContext.GetInvalidEmailCache(tmp.Email); ok || !tmp.Active {
								if ok := resourceContext.GetUpdateUndeliverableValidEmail(tmp.Email); ok {
									resourceContext.DeleteUpdateUndeliverableValidEmail(tmp.Email)
								}

								tmp.IsInvalid = true
							}
						}

						// If customer has defined the identity status (alias), use that
						if isAlias, ok := resourceContext.GetAliasUpdate(tmp.Email); ok {
							tmp.IsUser, hasName = !isAlias, !isAlias
						}

						resourceContext.SetUserResource(usrRscKey, tmp)
						resourceContext.SetAliasUpdate(tmp.Email, !hasName)

					}
				}
			}
		}
		nameList = make(map[string][]string)
	}
}
