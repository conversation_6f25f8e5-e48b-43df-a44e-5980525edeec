package context

import (
	"cmp"
	"encoding/json"
	"regexp"
	"slices"
	"strings"
	"sync"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func isOwnerKey(tagKey string) bool {

	if _, ok := OwnerTagKeys[strings.ToLower(tagKey)]; ok {
		return true
	}

	for k := range OwnerTagKeys {
		if strings.Contains(strings.ToLower(tagKey), k) {
			return true
		}
	}

	for r := range OwnerTagKeysRegex {
		regex := regexp.MustCompile(r)
		if regex.MatchString(strings.ToLower(tagKey)) {
			return true
		}
	}

	return false
}

func isOwnerText(text, tenantID string) (isOwner bool) {

	nameList := map[string][]string{text: {""}}
	exampleNames := map[string]bool{
		"John Doe":    true,
		"Engineering": false,
		"Aniket":      true,
		"DevOps":      false,
	}

	resp := common.HumanOrNonHumanName(nameList, exampleNames, tenantID)
	for _, hasName := range resp {
		if hasName {
			isOwner = true
		}
	}

	return
}

func GetUserContextItem(username, userType, desc, identityId string, event *common.ResourceCtxEvent, r *ResourceContext) (resourceCtxItem common.ResourceContextItem) {

	if len(username) <= 0 {
		return
	}

	if vals, ok := r.GetTypoExceptions(username); ok && len(vals) == 1 {
		username = vals[0]
	}

	var (
		name           string
		isEmailActive  = true
		isValidEmail   bool
		emailStatusMap map[string]bool
	)

	if strings.HasSuffix(username, IAM_USER_SUFFIX) || strings.HasSuffix(username, APP_USER_SUFFIX) ||
		strings.HasSuffix(username, IAM_ROLE_SUFFIX) || strings.HasSuffix(username, SERVICEACCOUNT_USER_SUFFIX) ||
		strings.HasSuffix(username, ACCOUNT_USER_SUFFIX) || strings.HasSuffix(username, AWSSERVICE_USER_SUFFIX) || strings.HasSuffix(username, SERVICENAME_USER_SUFFIX) {

		r.SetUserResource(NAME_ONLY_PREFIX+strings.ToLower(username), UserContext{
			Name: username,
		})
	} else {

		if r.ServiceID == common.GCP_SERVICE_ID {

			// Because GCP labels doesn't allow special characters, so @ not supported

			if _, err := common.ParseAddress(username); err != nil {

				for _, domain := range emailDomains {

					if strings.HasSuffix(username, domain) {

						// In House method
						derivedEmail, err := deriveEmailFromGCPTag(username, r)
						if err != nil && strings.Contains(err.Error(), INVALID_EMAIL_ERROR) {
							isEmailActive = false
						}

						if len(derivedEmail) > 0 {
							username = derivedEmail
						}
					}
				}
			}
		}

		if _, err := common.ParseAddress(username); err != nil {
			nameOnlyUserResource := username
			nameList := strings.Split(username, " ")
			if len(nameList) > 1 {
				// names with single word should not be converted to title case (might cause issues in email derivation)
				nameOnlyUserResource = common.ConvertToTitleCase(nameOnlyUserResource)
			}

			name = common.GetFormattedName(nameOnlyUserResource)
			if isOwnerText(name, r.TenantID) {
				email := deriveEmailFromName(name, r)
				if len(email) > 0 {
					username = email
				} else {
					r.SetUserResource(NAME_ONLY_PREFIX+strings.ToLower(username), UserContext{
						Name: nameOnlyUserResource,
					})
				}
			}
		}

		if originalEmailAddr, err := common.ParseAddress(username); err == nil {

			temp := UserContext{
				Email: originalEmailAddr.Address,
				Name:  originalEmailAddr.Name,
			}

			temp.IsSddl = handleUserContextWithSDDL(&temp.Email, &name, r)

			if strings.Contains(strings.ToLower(username), EXT_KEYWORD) {
				handleIdentityWithExternalEmail(r, &temp)
			}

			if strings.Contains(strings.ToLower(username), LIVE_MICROSOFT_KEYWORD) || strings.Contains(strings.ToLower(username), MAIL_MICROSOFT_KEYWORD) {
				handlePersonalAndMicrosoftEmail(r, &temp)
			}

			if addr, err := common.ParseAddress(temp.Email); err == nil {

				if len(temp.Name) > 0 {
					name = temp.Name
				}

				if len(name) <= 0 {
					name = common.GetFormattedNameFromEmail(addr.Address)
				}

				if temp.IsSddl {
					name = removeSddlFromName(name)
				}

				if userResource, ok := r.GetUserResource(addr.Address); !ok {

					if isEmailActive {

						if isValidEmail, ok = r.GetEmailStatus(addr.Address); !ok && len(name) > 0 {
							emailNameMap := map[string]string{
								addr.Address:              name,
								originalEmailAddr.Address: name,
							}

							if emailStatusMap, err = checkEmailValidity(emailNameMap, false, r); err == nil {
								isValidEmail = len(emailStatusMap) > 0 && emailStatusMap[addr.Address]
								r.SetEmailStatus(addr.Address, isValidEmail)
							}
						}

						// if addr.Address status does not throw error update activate Employee flag
						if err == nil {
							isEmailActive = isValidEmail
						}
					}

					temp.Active = isEmailActive
					if ok := r.GetInvalidEmailCache(temp.Email); ok {
						temp.IsInvalid = true
					}

					if fullName, ok := r.GetEmailToFullName(addr.Address); ok && len(fullName) > len(name) {
						name = fullName
					}

					temp.Name = name
					HandleChildEmail(r, &temp)

					r.SetUserResource(addr.Address, temp)

				} else {

					if len(userResource.Name) < len(name) {
						userResource.Name = name
					}

					r.SetUserResource(userResource.Email, userResource)
				}

				// updating username since addr will contain trimmed email (sddl)
				username = addr.Address

			}
		}
	}

	resourceCtxItem = common.ResourceContextItem{
		Name:       username,
		Type:       userType,
		IdentityId: identityId,
	}

	if len(desc) > 0 {
		resourceCtxItem.Desc = desc
	} else {
		resourceCtxItem.Desc = GetStaticDescriptionOfUserType(resourceCtxItem.Type)
	}

	if event != nil {
		resourceCtxItem.Event = event
	}

	return resourceCtxItem
}

func GetStaticDescriptionOfUserType(userType string) (desc string) {

	switch userType {

	case common.ACCOUNT_OWNER_USER_TYPE:
		desc = "User has been assigned the owner of the account"
	case common.ACCOUNT_CONTACT_USER_TYPE:
		desc = "User has been assigned as an alternate contact for the account"
	case common.PROJECT_OWNER_USER_TYPE:
		desc = "User has owner or equivalent role in the project"
	case common.ORG_OWNER_USER_TYPE:
		desc = "User has owner or equivalent role in the organization"
	case common.FOLDER_OWNER_USER_TYPE:
		desc = "User has owner or equivalent role in the folder"
	case common.PARENTFOLDER_OWNER_USER_TYPE:
		desc = "User has owner or equivalent role in a parent folder"
	case common.SUBSCRIPTION_CONTACT_USER_TYPE:
		desc = "User has been assigned as an alternate contact for the subscription"
	case common.NO_CHILD_OWNERS_PARENT_OWNER_USER_TYPE:
		desc = "Account owner(s) is the sole user of all resources under it"
	case common.FEW_CHILD_OWNERS_PARENT_OWNER_USER_TYPE:
		desc = "Account is being used only by 1 or 2 users"
	case common.RELATED_RESOURCE_OWNER_USER_TYPE:
		desc = "User owns one or more resources related to this resource"
	case common.RESOURCE_NAME_USER_TYPE:
		desc = "Resource is named after the user"
	case common.RESOURCE_TYPE_OWNER:
		desc = "Service is being managed mostly by this user in this account"
	case common.RESOURCE_OWNER_USER_TYPE:
		desc = "User has been specified as an owner in resource properties"
	case common.RESOURCE_OWNERAPP_USER_TYPE:
		desc = "Application has been specified as an owner in resource properties"
	case common.OPENAIPROJECT_OWNER_USER_TYPE:
		desc = "User has been assigned the owner of the project"
	case common.OPENAIORG_OWNER_USER_TYPE:
		desc = "User has been assigned the owner of the organization"
	case common.CUSTOMER_DEFINED_USER_TYPE:
		desc = "User has been assigned as the owner from Precize console"
	case common.ORG_ACCOUNT_OWNER_USER_TYPE:
		desc = "User has been assigned the owner of the organization"
	case common.PRECIZE_DEFINED_USER_TYPE:
		desc = "User has been derived as the owner by Precize"
	case common.RESOURCE_OPS_CONTACT_USER_TYPE:
		desc = "User has been assigned as the ops contact for the resource"
	case common.PRECIZE_DETECTED_USER_TYPE:
		desc = "Precize detected this resource"
	case common.MANAGER_OWNER_USER_TYPE:
		desc = "User is the manager of this identity"
	}

	return
}

var SortUsername = func(a, b common.ResourceContextItem) int {

	if (!strings.HasPrefix(a.Name, EX_EMPLOYEE_PREFIX) && strings.HasPrefix(b.Name, EX_EMPLOYEE_PREFIX)) || (!strings.HasPrefix(a.Name, INVALID_EMPLOYEE_PREFIX) && strings.HasPrefix(b.Name, INVALID_EMPLOYEE_PREFIX)) {
		return -1
	} else if (strings.HasPrefix(a.Name, EX_EMPLOYEE_PREFIX) && !strings.HasPrefix(b.Name, EX_EMPLOYEE_PREFIX)) || (strings.HasPrefix(a.Name, INVALID_EMPLOYEE_PREFIX) && !strings.HasPrefix(b.Name, INVALID_EMPLOYEE_PREFIX)) {
		return 1
	}

	notEmailA, _ := common.ParseAddress(a.Name)
	notEmailB, _ := common.ParseAddress(b.Name)

	if notEmailA != nil && notEmailB == nil {
		return 1
	} else if notEmailA == nil && notEmailB != nil {
		return -1
	}

	return cmp.Compare(strings.ToLower(a.Name), strings.ToLower(b.Name))
}

var SortUsernameString = func(a, b string) int {

	if (!strings.HasPrefix(a, EX_EMPLOYEE_PREFIX) && strings.HasPrefix(b, EX_EMPLOYEE_PREFIX)) || (!strings.HasPrefix(a, INVALID_EMPLOYEE_PREFIX) && strings.HasPrefix(b, INVALID_EMPLOYEE_PREFIX)) {
		return -1
	} else if (strings.HasPrefix(a, EX_EMPLOYEE_PREFIX) && !strings.HasPrefix(b, EX_EMPLOYEE_PREFIX)) || (strings.HasPrefix(a, INVALID_EMPLOYEE_PREFIX) && !strings.HasPrefix(b, INVALID_EMPLOYEE_PREFIX)) {
		return 1
	}

	notEmailA, _ := common.ParseAddress(a)
	notEmailB, _ := common.ParseAddress(b)

	if notEmailA != nil && notEmailB == nil {
		return 1
	} else if notEmailA == nil && notEmailB != nil {
		return -1
	}

	return cmp.Compare(strings.ToLower(a), strings.ToLower(b))
}

func getInheritedOwners(parentDoc common.ResourceContextInsertDoc, parentType string) (inheritedOwners []common.ResourceContextItem) {

	for _, parentDefined := range parentDoc.DefinedOwners {

		inheritedOwners = append(inheritedOwners, common.ResourceContextItem{
			Name: parentDefined.Name,
			Type: "Parent " + common.ConvertToTitleCase(parentType) + " Defined Owner",
			Desc: "User is the owner of the parent " + common.ConvertToTitleCase(parentType),
		})
	}

	for _, parentDerived := range parentDoc.DerivedOwners {

		inheritedOwners = append(inheritedOwners, common.ResourceContextItem{
			Name: parentDerived.Name,
			Type: "Parent " + common.ConvertToTitleCase(parentType) + " Derived Owner",
			Desc: "User is the owner of the parent " + common.ConvertToTitleCase(parentType),
		})
	}

	return
}

func getInheritedOwnersForDefaultResources(parentDoc common.ResourceContextInsertDoc, parentType string) (inheritedOwners []common.ResourceContextItem) {

	for _, parentDefined := range parentDoc.DefinedOwners {

		inheritedOwners = append(inheritedOwners, common.ResourceContextItem{
			Name: parentDefined.Name,
			Type: "Parent " + common.ConvertToTitleCase(parentType) + " Defined Owner",
			Desc: "User owns the parent " + strings.ToLower(parentType) + " of this default resource created by the service provider",
		})
	}

	for _, parentDerived := range parentDoc.DerivedOwners {

		inheritedOwners = append(inheritedOwners, common.ResourceContextItem{
			Name: parentDerived.Name,
			Type: "Parent " + common.ConvertToTitleCase(parentType) + " Derived Owner",
			Desc: "User owns the parent " + strings.ToLower(parentType) + " of this default resource created by the service provider",
		})
	}

	return
}

func sortInheritedOwners(inheritedOwners []common.ResourceContextItem) []common.ResourceContextItem {

	var (
		defined, derived []common.ResourceContextItem
	)

	for _, inheritedOwner := range inheritedOwners {
		if strings.Contains(inheritedOwner.Type, "Defined") {
			defined = append(defined, inheritedOwner)
		} else {
			derived = append(derived, inheritedOwner)
		}
	}

	slices.SortFunc(defined, SortUsername)

	return append(defined, derived...)
}

func IncrementParentChildOwnerCount(resourceContext *ResourceContext, owner, accountID string) {

	if len(accountID) > 0 {
		var ownerCount map[string]int
		if eOwnerCount, ok := resourceContext.GetParentChildOwner(accountID); ok {
			ownerCount = eOwnerCount
		} else {
			ownerCount = make(map[string]int)
		}

		ownerCount[owner]++
		resourceContext.SetParentChildOwner(accountID, ownerCount)
	}
}

func GetMaxActivityOwnersOfResourceType(resourceTypeOwners map[string]map[string]int, resourceType string) (owners []string) {

	if typeOwners, ok := resourceTypeOwners[resourceType]; ok {

		var (
			max       int
			threshold = len(typeOwners) / 100 // 1 percent of total count of resources in that resource type
		)

		for ownerKey, count := range typeOwners {
			if count > threshold {
				if count > max {
					max = count
					owners = []string{ownerKey}
				} else if count == max {
					owners = append(owners, ownerKey)
				}
			}
		}
	}

	slices.SortFunc(owners, SortUsernameString)

	// Upto two max owners allowed in case of tie
	if len(owners) > 2 {
		owners = []string{}
	}

	return
}

func IncrementResourceTypeOwnerCount(resourceContext *ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, uniqueOwners []string) {
	if len(resourceContextDoc.Account) > 0 {
		if resourceTypeOwnerAccMap, ok := resourceContext.GetResourceTypeOwner(resourceContextDoc.Account); !ok {
			resourceTypeOwnerAccMap = make(map[string]map[string]int)
			resourceContext.SetResourceTypeOwner(resourceContextDoc.Account, resourceTypeOwnerAccMap)
		} else if len(resourceContextDoc.ResourceType) > 0 {
			if _, ok := resourceTypeOwnerAccMap[resourceContextDoc.ResourceType]; !ok {
				resourceTypeOwnerAccMap[resourceContextDoc.ResourceType] = make(map[string]int)
			}

			for _, derivedOwner := range resourceContextDoc.ResourceOwnerTypes.DerivedOwners {
				if derivedOwner.Type == common.ACTIVITY_USER_TYPE {
					if slices.Contains(uniqueOwners, derivedOwner.Name) {
						resourceTypeOwnerAccMap[resourceContextDoc.ResourceType][derivedOwner.Name]++
					}
				}
			}

			// Store the updated map back to the concurrent map
			resourceContext.SetResourceTypeOwner(resourceContextDoc.Account, resourceTypeOwnerAccMap)
		}
	}
}

type ownerListParams struct {
	owners           []string
	uniqueOwners     map[string]int
	reassignedOwners []common.ResourceContextItem
}

func PostProcessOwners(resourceContextDoc *common.ResourceContextInsertDoc, rCtx *ResourceContext) []string {

	var (
		ownerList  ownerListParams
		ownerTypes = resourceContextDoc.ResourceOwnerTypes
	)

	ownerList.uniqueOwners = make(map[string]int)

	for i := range ownerTypes.DefinedOwners {
		rctxItem := ownerTypes.DefinedOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, true, false)
		ownerTypes.DefinedOwners[i] = rctxItem
	}
	ownerTypes.DefinedOwners = removeDuplicateOwners(ownerTypes.DefinedOwners)
	slices.SortFunc(ownerTypes.DefinedOwners, SortUsername)
	slices.SortFunc(ownerList.owners, SortUsernameString)

	for i := range ownerTypes.CodeOwners {
		rctxItem := ownerTypes.CodeOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, true, false)
		ownerTypes.CodeOwners[i] = rctxItem
	}
	ownerTypes.CodeOwners = removeDuplicateOwners(ownerTypes.CodeOwners)

	for i := range ownerTypes.DerivedOwners {
		rctxItem := ownerTypes.DerivedOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, true, false)
		ownerTypes.DerivedOwners[i] = rctxItem
	}
	ownerTypes.DerivedOwners = removeDuplicateOwners(ownerTypes.DerivedOwners)

	for i := range ownerTypes.InheritedOwners {

		var isOwner bool

		if strings.Contains(ownerTypes.InheritedOwners[i].Type, common.AZURE_RG_RESOURCE_TYPE) {
			isOwner = true
		}

		rctxItem := ownerTypes.InheritedOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, isOwner, true)
		ownerTypes.InheritedOwners[i] = rctxItem
	}
	ownerTypes.InheritedOwners = removeDuplicateOwners(ownerTypes.InheritedOwners)
	ownerTypes.InheritedOwners = sortInheritedOwners(ownerTypes.InheritedOwners)

	for i := range ownerTypes.CostOwners {
		rctxItem := ownerTypes.CostOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, false, false)
		ownerTypes.CostOwners[i] = rctxItem
	}
	ownerTypes.CostOwners = removeDuplicateOwners(ownerTypes.CostOwners)

	for i := range ownerTypes.SecurityOwners {
		rctxItem := ownerTypes.SecurityOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, false, false)
		ownerTypes.SecurityOwners[i] = rctxItem
	}
	ownerTypes.SecurityOwners = removeDuplicateOwners(ownerTypes.SecurityOwners)

	for i := range ownerTypes.OpsOwners {
		rctxItem := ownerTypes.OpsOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, false, false)
		ownerTypes.OpsOwners[i] = rctxItem
	}
	ownerTypes.OpsOwners = removeDuplicateOwners(ownerTypes.OpsOwners)

	if len(resourceContextDoc.ResourceName) > 0 {

		isOwner := true

		switch resourceContextDoc.ResourceType {
		case common.AZURE_ADUSER_RESOURCE_TYPE, common.AWS_SSOUSER_RESOURCE_TYPE, common.GCP_IAM_RESOURCE_TYPE, common.AWS_ROOTUSER_RESOURCE_TYPE, common.AZURE_USEROWNER_RESOURCE_TYPE:
			isOwner = false

		case common.AWS_IAM_USER_RESOURCE_TYPE:
			if !resourceContextDoc.HasConsoleLogin {
				// IAM Identities without login should be added to users list
				isOwner = true
			} else {
				// Don't add as owner, as the person the identity is for, might not be the one incharge of that resource
				isOwner = false
			}
		}

		rctxItem := common.ResourceContextItem{Name: resourceContextDoc.ResourceName + RESOURCE_NAME_TEMP_USER_SUFFIX}

		derivedOwnerFromResourceName := processAndSetUniqueOwner(
			&rctxItem,
			&ownerList, rCtx, isOwner, false)

		if len(derivedOwnerFromResourceName) > 0 {
			ownerTypes.DerivedOwners = append([]common.ResourceContextItem{
				{
					Name:            derivedOwnerFromResourceName,
					Type:            common.RESOURCE_NAME_USER_TYPE,
					Desc:            GetStaticDescriptionOfUserType(common.RESOURCE_NAME_USER_TYPE),
					IdentityId:      rctxItem.IdentityId,
					ChildIdentityID: rctxItem.ChildIdentityID,
				},
			}, ownerTypes.DerivedOwners...)
		}
	}

	ownerList.reassignedOwners = removeDuplicateOwners(ownerList.reassignedOwners)
	ownerTypes.DerivedOwners = append(ownerTypes.DerivedOwners, ownerList.reassignedOwners...)

	resourceContextDoc.ResourceOwnerTypes = ownerTypes

	return ownerList.owners
}

func removeDuplicateOwners(owners []common.ResourceContextItem) []common.ResourceContextItem {

	var (
		result []common.ResourceContextItem
		unique = make(map[string]struct{})
	)

	for _, owner := range owners {
		if len(owner.Name) > 0 {
			if _, ok := unique[owner.Name+owner.Desc]; !ok {
				unique[owner.Name+owner.Desc] = struct{}{}
				result = append(result, owner)
			}
		}
	}

	return result
}

func processAndSetUniqueOwner(rscContextItem *common.ResourceContextItem, ownerList *ownerListParams,
	r *ResourceContext, isOwner, isInherited bool) string {

	var (
		ok, serviceNameUser, resourceNameUser bool
		userResource                          UserContext
		name                                  = rscContextItem.Name
		nameBeforeProcessing                  = name
		includedOwner                         bool
		partialMatchValue                     PartialNameCache
	)

	if len(name) <= 0 {
		return name
	}

	if addr, err := common.ParseAddress(name); err == nil {

		// replace partner identity with parent identity
		if primaryEmail, ok := r.GetChildPrimaryEmail(rscContextItem.IdentityId); ok {
			rscContextItem.ChildIdentityID = rscContextItem.IdentityId
			addr.Address = primaryEmail
			rscContextItem.IdentityId = primaryEmail
		} else if primaryEmail, ok := r.GetChildPrimaryEmail(addr.Address); ok {
			rscContextItem.ChildIdentityID = addr.Address
			addr.Address = primaryEmail
			rscContextItem.IdentityId = primaryEmail
		}

		if len(addr.Name) <= 0 {
			if userResource, ok = r.GetUserResource(addr.Address); ok && len(userResource.Name) > 0 {
				name = formCompleteEmailFormat(userResource.Name, userResource.Email)
			} else {
				identityName := common.GetFormattedNameFromEmail(addr.Address)
				name = formCompleteEmailFormat(identityName, addr.Address)
			}
		} else {
			if userResource, ok = r.GetUserResource(addr.Address); ok && len(userResource.Name) > 0 {
				// Already complete email format
			}
		}
	} else {

		if strings.HasSuffix(name, RESOURCE_NAME_TEMP_USER_SUFFIX) {
			name = strings.TrimSuffix(name, RESOURCE_NAME_TEMP_USER_SUFFIX)
			resourceNameUser = true
		}

		if strings.HasSuffix(name, SERVICENAME_USER_SUFFIX) {
			name = strings.TrimSuffix(name, SERVICENAME_USER_SUFFIX)
			serviceNameUser = true
		}

		if strings.HasSuffix(name, IAM_USER_SUFFIX) || strings.HasSuffix(name, APP_USER_SUFFIX) ||
			strings.HasSuffix(name, IAM_ROLE_SUFFIX) || strings.HasSuffix(name, SERVICEACCOUNT_USER_SUFFIX) ||
			strings.HasSuffix(name, ACCOUNT_USER_SUFFIX) || strings.HasSuffix(name, AWSSERVICE_USER_SUFFIX) {

			if isInherited {
				return ""
			}

			return name
		}

		loweredName := strings.ToLower(name)
		cleanedName := common.RemoveSpecialCharactersFromString(strings.ToLower(loweredName))

		// Partial match
		if fetchedFromCache, ok := r.GetPartialNameMatch(cleanedName); !ok {

			var (
				matchedUsrRscKey    string
				matchedLength       int
				matchedFirstNameHit bool
				multiMatch          bool
			)

			r.RangeUserResources(func(userResourceKey string, usrRsrc UserContext) bool {

				if !strings.HasPrefix(userResourceKey, NAME_ONLY_PREFIX) { // Want to match only emails first

					if (resourceNameUser || serviceNameUser) && !usrRsrc.IsUser {
						// Resource named after user should only match human users
						return true
					}

					for userDepartment := range usrRsrc.Department {
						if _, ok := ExcludedDepartments[strings.ToLower(userDepartment)]; ok {
							for userJobTitle := range usrRsrc.JobTitle {
								cloudUser := false
								for _, keyword := range techKeywords {
									if strings.Contains(strings.ToLower(userJobTitle), keyword) {
										cloudUser = true
										break
									}
								}

								if !cloudUser {
									return true
								}
							}
						}
					}

					var (
						currentLength       int
						currentFirstNameHit bool
						userNameSplit       = common.SplitStringBySpecialCharacters(usrRsrc.Name)
					)

					// Check if each word in name can match

					for i, splitUser := range userNameSplit {

						splitUser = strings.ToLower(splitUser)

						if ownerMatchIncluded(r, splitUser, cleanedName, loweredName) {
							currentLength += len(splitUser)
							currentFirstNameHit = currentFirstNameHit || i == 0
							includedOwner = true
							continue
						}

						if len(splitUser) >= 5 && len(cleanedName) >= 5 {
							if strings.Contains(cleanedName, splitUser) {
								// Direct contains match if word length >= 5

								// jo_hnbucket should not be matched to john
								// john-bucket should be matched to john
								splitResourceName := common.SplitStringBySpecialCharacters(loweredName)
								properMatch := false

								for _, splitResource := range splitResourceName {
									if strings.Contains(splitResource, splitUser) {
										properMatch = true
										break
									}
								}

								if !properMatch {
									continue
								}

								if ownerMatchExcluded(r, splitUser, cleanedName) {
									continue
								}

								currentLength += len(splitUser)
								currentFirstNameHit = currentFirstNameHit || i == 0

							} else if strings.Contains(splitUser, cleanedName) {
								if ownerMatchExcluded(r, splitUser, cleanedName) {
									continue
								}

								currentLength += len(cleanedName)
								currentFirstNameHit = currentFirstNameHit || i == 0
							}
						} else if len(splitUser) >= 3 && len(cleanedName) >= 3 {
							// If 3-5 letter word check for exact match of word in b/w special characters
							splitName := common.SplitByNonAlphaNumericEfficient(strings.ToLower(name))

							for _, partialName := range splitName {
								if splitUser == partialName {
									if ownerMatchExcluded(r, splitUser, partialName) {
										continue
									}

									currentLength += len(splitUser)
									currentFirstNameHit = currentFirstNameHit || i == 0
									break
								}
							}
						}
					}

					if currentLength <= 0 {
						// Check for exact match of emailName in b/w special characters
						splitName := common.SplitByNonAlphaNumericEfficient(strings.ToLower(name))
						emailName := common.GetEmailNameWithoutSpecialCharacters(userResourceKey)

						for _, partialName := range splitName {
							if emailName == partialName {
								currentLength += len(userResourceKey)
								currentFirstNameHit = true
								break
							}
						}
					}

					comparator := compareCurrentUserWithMatchedUser(r, name, userResourceKey, matchedUsrRscKey, currentLength, matchedLength, currentFirstNameHit, matchedFirstNameHit)

					if comparator == 1 {
						matchedLength = currentLength
						matchedUsrRscKey = userResourceKey
						matchedFirstNameHit = currentFirstNameHit
						multiMatch = false
					} else if comparator == 2 {
						multiMatch = true
					}
				}

				return true
			})

			if len(matchedUsrRscKey) > 0 {

				// if matchedUsrRscKey email has a primary email replace with that
				if primaryEmail, ok := r.GetChildPrimaryEmail(matchedUsrRscKey); ok {
					if _, ok := r.GetUserResource(primaryEmail); ok {
						matchedUsrRscKey = primaryEmail
					}
				}

				if userResource, ok = r.GetUserResource(matchedUsrRscKey); ok {
					// Handle the case where there's no multiMatch or there is multi match the userResource is Sddl
					if !multiMatch || (multiMatch && userResource.IsSddl) {

						// replace partner identity with parent identity
						if primaryEmail, ok := r.GetChildPrimaryEmail(userResource.Email); ok {

							if tempUsr, ok := r.GetUserResource(primaryEmail); ok {

								userResource = tempUsr
								rscContextItem.ChildIdentityID = rscContextItem.IdentityId
								rscContextItem.IdentityId = primaryEmail
								matchedUsrRscKey = primaryEmail

							}
						}

						name = formCompleteEmailFormat(userResource.Name, userResource.Email)
						partialMatchValue = PartialNameCache{name: name, userResourceKey: matchedUsrRscKey}
						r.SetPartialNameMatch(cleanedName, partialMatchValue)
					} else {
						logger.Print(logger.INFO, "Multi match confirmed", name)
						partialMatchValue = PartialNameCache{name: "", userResourceKey: ""}
						r.SetPartialNameMatch(cleanedName, partialMatchValue)
						return ""
					}
				}
			} else if userResource, ok = r.GetUserResource(NAME_ONLY_PREFIX + strings.ToLower(name)); ok {
				partialMatchValue = PartialNameCache{name: name, userResourceKey: ""}
				r.SetPartialNameMatch(cleanedName, partialMatchValue)
			} else {
				partialMatchValue = PartialNameCache{name: "", userResourceKey: ""}
				r.SetPartialNameMatch(cleanedName, partialMatchValue)
				return ""
			}
		} else if len(fetchedFromCache.name) > 0 && len(fetchedFromCache.userResourceKey) > 0 {
			name = fetchedFromCache.name
			userResource, _ = r.GetUserResource(fetchedFromCache.userResourceKey)

		} else if len(fetchedFromCache.name) > 0 {
			name = fetchedFromCache.name
		} else {
			return ""
		}

	}

	if len(userResource.Email) > 0 && (!userResource.Active || userResource.IsInvalid) {

		if userResource.IsInvalid {
			name = INVALID_EMPLOYEE_PREFIX + name
		} else {
			name = EX_EMPLOYEE_PREFIX + name
		}

		// Reassigned user for resource

		if len(userResource.Reassigned.Name) > 0 && isOwner {

			ownerList.reassignedOwners = append(ownerList.reassignedOwners, userResource.Reassigned)
			if _, ok := ownerList.uniqueOwners[userResource.Reassigned.Name]; !ok {
				ownerList.uniqueOwners[userResource.Reassigned.Name]++
				ownerList.owners = append(ownerList.owners, userResource.Reassigned.Name)
			}
		}
	}

	trimmedServiceNameUser := strings.TrimSuffix(nameBeforeProcessing, SERVICENAME_USER_SUFFIX)
	if serviceNameUser && (name == trimmedServiceNameUser || !userResource.IsUser || !common.IsValidOwnerAttribution(trimmedServiceNameUser, name, r.TenantID, includedOwner)) {
		if name != trimmedServiceNameUser {
			logger.Print(logger.INFO, "Service name user match rejected", trimmedServiceNameUser, name)
		}
		return ""
	}

	trimmedResourceNameUser := strings.TrimSuffix(nameBeforeProcessing, RESOURCE_NAME_TEMP_USER_SUFFIX)
	if resourceNameUser && (name == trimmedResourceNameUser || !userResource.IsUser || !common.IsValidOwnerAttribution(trimmedResourceNameUser, name, r.TenantID, includedOwner)) {
		if name != trimmedResourceNameUser {
			logger.Print(logger.INFO, "Resource name match rejected", trimmedResourceNameUser, name)
		}
		return ""
	}

	if len(partialMatchValue.name) > 0 {
		logger.Print(logger.INFO, "Partial name matched", nameBeforeProcessing, partialMatchValue)
	}

	if isInherited && !userResource.IsUser {
		return ""
	}

	if len(userResource.Email) > 0 && !userResource.IsUser {
		isOwner = false
	}

	if isOwner {

		if resourceNameUser {
			// Resource name user owner should come in the beginning.
			// Delete if already exists somewhere else in the list
			if slices.Contains(ownerList.owners, name) && ownerList.owners[0] != name {
				ownerList.owners = slices.DeleteFunc(ownerList.owners, func(s string) bool {
					return s == name
				})
			}

			ownerList.owners = append([]string{name}, ownerList.owners...)

		} else if _, ok := ownerList.uniqueOwners[name]; !ok {
			ownerList.uniqueOwners[name] = 0
			ownerList.owners = append(ownerList.owners, name)
		}
	}

	return name
}

func ownerMatchIncluded(r *ResourceContext, str1, str2, resourceNameUnmodified string) bool {

	if vals, ok := r.GetOwnerInclusions(str1); ok {
		for _, val := range vals {
			if len(val) <= 3 {
				// For small values, check if word is a whole word match
				if common.MatchWordBoundary(val, resourceNameUnmodified) {
					return true
				}
			} else {
				if strings.Contains(str2, val) || strings.Contains(val, str2) {
					return true
				}
			}
		}
	}

	return false
}

func ownerMatchExcluded(r *ResourceContext, str1, str2 string) bool {

	if len(GetAppNameFromValue(str1)) > 0 {
		logger.Print(logger.INFO, "Excluded owner match because of app name", str1, str2)
		return true
	}

	if len(GetSoftwareNameFromValue(str1)) > 0 {
		logger.Print(logger.INFO, "Excluded owner match because of software name", str1, str2)
		return true
	}

	if vals, ok := r.GetOwnerExceptions(str1); ok {
		for _, val := range vals {
			if strings.Contains(str2, val) {
				return true
			}
		}
	}

	return false
}

func compareCurrentUserWithMatchedUser(r *ResourceContext, name, currentUsrRscKey string, matchedUsrRscKey string, currentLength int, matchedLength int, currentFirstNameHit bool, matchedFirstNameHit bool) int {

	// 0 is no change - matched remains
	// 1 is change - current replaces
	// 2 is multimatch

	if currentLength == 0 {
		return 0
	}

	if matchedLength == 0 {
		return 1
	}

	// If one is human and the other is not
	if matchedUserResource, ok := r.GetUserResource(matchedUsrRscKey); ok {
		if currentUserResource, ok := r.GetUserResource(currentUsrRscKey); ok {
			if currentUserResource.IsUser && !matchedUserResource.IsUser {
				logger.Print(logger.INFO, "User match changed due to human preference", name, currentUsrRscKey, matchedUsrRscKey)
				return 1
			} else if matchedUserResource.IsUser && !currentUserResource.IsUser {
				logger.Print(logger.INFO, "User match persisted due to human preference", name, matchedUsrRscKey, currentUsrRscKey)
				return 0
			}
		}
	}

	// If match length is more
	if currentLength > matchedLength {
		logger.Print(logger.INFO, "User match changed due to more character match", name, currentUsrRscKey, matchedUsrRscKey)
		return 1
	} else if matchedLength > currentLength {
		logger.Print(logger.INFO, "User match persisted due to more character match", name, matchedUsrRscKey, currentUsrRscKey)
		return 0
	}

	// If one matches first name and the other doesn't
	if currentFirstNameHit && !matchedFirstNameHit {
		logger.Print(logger.INFO, "User match changed due to first name preference", name, currentUsrRscKey, matchedUsrRscKey)
		return 1
	} else if matchedFirstNameHit && !currentFirstNameHit {
		logger.Print(logger.INFO, "User match persisted due to first name preference", name, matchedUsrRscKey, currentUsrRscKey)
		return 0
	}

	var (
		currentUsrRscKeyPriority, matchedUsrRscKeyPriority int
	)

	for i, primaryDomain := range r.PrimaryDomains {
		if strings.HasSuffix(currentUsrRscKey, primaryDomain) {
			currentUsrRscKeyPriority = len(r.PrimaryDomains) - i
		}

		if strings.HasSuffix(matchedUsrRscKey, primaryDomain) {
			matchedUsrRscKeyPriority = len(r.PrimaryDomains) - i
		}
	}

	if currentUsrRscKeyPriority > matchedUsrRscKeyPriority {
		// If one is primary domain and another is not or lesser priority primary domain
		logger.Print(logger.INFO, "User match changed due to higher priority domain", name, currentUsrRscKey, matchedUsrRscKey)
		return 1
	} else if matchedUsrRscKeyPriority > currentUsrRscKeyPriority {
		logger.Print(logger.INFO, "User match persisted due to higher priority domain", name, matchedUsrRscKey, currentUsrRscKey)
		return 0
	}

	logger.Print(logger.INFO, "Potential multi match", name, currentUsrRscKey, matchedUsrRscKey)
	return 2
}

func getNamesFromDescription(desc, tenantID string) []string {

	namesList := make([]string, 0)
	namesStr := common.DeriveNamesStringFromDescription(desc, tenantID)

	if namesStr == "" {
		return namesList
	}

	namesList = strings.Split(namesStr, ",")
	for i, name := range namesList {
		name = strings.Trim(name, " ")
		namesList[i] = name
	}

	return namesList
}

func PostProcessServiceIdentities(resourceContext *ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, uniqueOwners *sync.Map, enhancedUniqueOwners *[]string) {

	switch resourceContextDoc.ServiceID {

	case common.GCP_SERVICE_ID_INT:

		if resourceContextDoc.ResourceType == common.GCP_SERVICEACCOUNT_RESOURCE_TYPE || resourceContextDoc.ResourceType == common.GCP_SAPOLICYBINDING_RESOURCE_TYPE {
			serviceAccountContextID := common.GenerateCombinedHashID(resourceContextDoc.ResourceID, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContext.TenantID)
			if resourceContextDoc.ResourceType == common.GCP_SAPOLICYBINDING_RESOURCE_TYPE {
				serviceAccountContextID = common.GenerateCombinedHashID(resourceContextDoc.ResourceID, common.GCP_SAPOLICYBINDING_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContext.TenantID)
			}
			if relatedResources, ok := resourceContext.GetRelatedResourceList(serviceAccountContextID); ok {
				if len(relatedResources) <= 2 {
					for _, relatedResource := range relatedResources {
						if relatedResource.ResourceType != common.GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE &&
							relatedResource.ResourceType != common.GCP_SERVICEACCOUNT_RESOURCE_TYPE &&
							relatedResource.ResourceType != common.GCP_SAPOLICYBINDING_RESOURCE_TYPE {

							if ownersVal, ok := uniqueOwners.Load(relatedResource.ResourceDocID); ok {

								uniqueResourceOwners, validCast := ownersVal.([]string)
								if validCast {
									if len(uniqueResourceOwners) > 0 {

										attachedResourcePrimaryOwner := uniqueResourceOwners[0]

										resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners,
											common.ResourceContextItem{
												Name: attachedResourcePrimaryOwner,
												Type: common.ATTACHEDRESOURCE_OWNER_USER_TYPE,
												Desc: "User owns a resource that has this Service Account attached to it",
											},
										)

										if !slices.Contains(*enhancedUniqueOwners, attachedResourcePrimaryOwner) {
											*enhancedUniqueOwners = append(*enhancedUniqueOwners, attachedResourcePrimaryOwner)
										}
									}
								}

							}
						}
					}
				}
			}
		}

		for _, derivedOwner := range resourceContextDoc.ResourceOwnerTypes.DerivedOwners {
			if strings.HasSuffix(derivedOwner.Name, SERVICEACCOUNT_USER_SUFFIX) {
				serviceAccount := strings.TrimSuffix(derivedOwner.Name, SERVICEACCOUNT_USER_SUFFIX)
				serviceAccountContextID := common.GenerateCombinedHashID(serviceAccount, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContext.TenantID)
				if _, ok := resourceContext.GetResourceContextInsertDoc(serviceAccountContextID); !ok {
					serviceAccountContextID = common.GenerateCombinedHashID(serviceAccount, common.GCP_SAPOLICYBINDING_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContext.TenantID)
				}

				var attachedResourcePrimaryOwners []string

				if servAccRelatedRsc, ok := resourceContext.GetRelatedResourceList(serviceAccountContextID); ok {
					for _, relatedResource := range servAccRelatedRsc {
						if relatedResource.ResourceType != common.GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE &&
							relatedResource.ResourceType != common.GCP_SERVICEACCOUNT_RESOURCE_TYPE &&
							relatedResource.ResourceType != common.GCP_SAPOLICYBINDING_RESOURCE_TYPE {
							if relRscOwner, ok := uniqueOwners.Load(relatedResource.ResourceDocID); ok {

								relatedResourceOwners, validCast := relRscOwner.([]string)
								if validCast && len(relatedResourceOwners) > 0 {

									attachedResourcePrimaryOwners = append(attachedResourcePrimaryOwners,
										relatedResourceOwners[0])
								}
							}
						}
					}
				}

				if len(attachedResourcePrimaryOwners) <= 2 {

					for _, attachedResourcePrimaryOwner := range attachedResourcePrimaryOwners {

						resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners,
							common.ResourceContextItem{
								Name: attachedResourcePrimaryOwner,
								Type: common.ATTACHEDRESOURCE_OWNER_USER_TYPE,
								Desc: "User owns a resource that has the Service Account " + serviceAccount + " attached which owns this resource",
							},
						)

						if !slices.Contains(*enhancedUniqueOwners, attachedResourcePrimaryOwner) {
							*enhancedUniqueOwners = append(*enhancedUniqueOwners, attachedResourcePrimaryOwner)
						}
					}
				}
			}
		}
	}
}

type RelatedOwner struct {
	Count               int
	Resources           map[string][]string
	Via                 []string
	Direct, IacRelation bool
}

func GetMaxRelatedOwner(resourceContext *ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, docID string, uniqueOwners *sync.Map,
	maxCount *int, maxOwner *string, via string, relatedOwners map[string]RelatedOwner, processedDocIDs map[string]struct{}, depth int, iacRelation bool) {

	var (
		// For direct relations
		firstDepthMaxCount      int
		firstDepthMaxOwner      string
		firstDepthRelatedOwners = make(map[string]RelatedOwner)
	)

	processedDocIDs[docID] = struct{}{}

	if relatedResources, ok := resourceContext.GetRelatedResourceList(docID); ok {

		for i, relatedResource := range relatedResources {

			if relatedResource.ResourceDocID == docID {
				continue
			}

			if relatedResource.ResourceType == resourceContextDoc.ResourceType {
				// Do not consider relations of the same resource type
				continue
			}

			if !relatedResource.ContextualRelation {
				// Do not consider relations which should not exchange contextual information
				continue
			}

			resourceIDSplit := strings.Split(relatedResource.ResourceID, "/")
			resource := resourceIDSplit[len(resourceIDSplit)-1]
			resourceType := relatedResource.ResourceType

			if ownersVal, ok := uniqueOwners.Load(relatedResource.ResourceDocID); ok {
				relatedResourceOwners, validCast := ownersVal.([]string)
				if validCast {
					for _, relatedOwner := range relatedResourceOwners {
						temp := relatedOwners[relatedOwner]
						temp.Count++

						if len(temp.Resources) <= 0 {
							temp.Resources = make(map[string][]string)
						}
						if len(temp.Resources) < 2 && len(temp.Resources[resourceType]) < 2 {

							if !slices.Contains(temp.Resources[resourceType], resource) {
								temp.Resources[resourceType] = append(temp.Resources[resourceType], resource)

								if depth > 1 {
									if !slices.Contains(temp.Via, via) {
										temp.Via = append(temp.Via, via)
									}

									if iacRelation {
										temp.IacRelation = true
									}
								}
							}
						}

						if temp.Count > *maxCount {
							*maxCount = temp.Count
							*maxOwner = relatedOwner
						}

						relatedOwners[relatedOwner] = temp

						if depth == 1 {

							if relatedResource.Priority {

								// Priority resources for direct relationships - like EC2 key for EC2

								*maxOwner = relatedOwner

								temp.Resources = make(map[string][]string)
								temp.Resources[resourceType] = append([]string{resource}, temp.Resources[resourceType]...)
								temp.Direct = true

								relatedOwners[relatedOwner] = temp
								return
							}

							firstDepthTemp := firstDepthRelatedOwners[relatedOwner]
							firstDepthTemp.Count++
							firstDepthTemp.Direct = true

							if len(firstDepthTemp.Resources) <= 0 {
								firstDepthTemp.Resources = make(map[string][]string)
							}
							if len(firstDepthTemp.Resources) < 2 && len(firstDepthTemp.Resources[resourceType]) < 2 {

								if !slices.Contains(firstDepthTemp.Resources[resourceType], resource) {
									firstDepthTemp.Resources[resourceType] = append(firstDepthTemp.Resources[resourceType], resource)
								}
							}

							if firstDepthTemp.Count > firstDepthMaxCount {
								firstDepthMaxCount = firstDepthTemp.Count
								firstDepthMaxOwner = relatedOwner
							}
							firstDepthRelatedOwners[relatedOwner] = firstDepthTemp
						}
					}
				}
			}

			if i == len(relatedResources)-1 && len(firstDepthMaxOwner) > 0 {
				// If direct relationships has a proper max owner, prioritise that
				*maxOwner = firstDepthMaxOwner

				for k := range relatedOwners {
					delete(relatedOwners, k)
				}

				for k, v := range firstDepthRelatedOwners {
					relatedOwners[k] = v
				}

				return
			}

			if _, ok := processedDocIDs[relatedResource.ResourceDocID]; !ok && depth < 2 && !iacRelation {

				if relatedResource.ResourceType == common.AWS_CFTSTACK_RESOURCE_TYPE || relatedResource.ResourceType == common.PRECIZEINTERNAL_COMMITFILE_RESOURCE_TYPE {
					iacRelation = true
				}

				if depth == 1 {
					// Directly related resource through which other indirect relations happen
					via = resource + " (" + relatedResource.ResourceType + ")"
				}

				GetMaxRelatedOwner(resourceContext, resourceContextDoc, relatedResource.ResourceDocID, uniqueOwners, maxCount, maxOwner, via, relatedOwners, processedDocIDs, depth+1, iacRelation)
			}
		}
	}
}

func GetAllOwnerExceptionsForTenant(resourceContext *ResourceContext) {
	ownerExemptionsQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"type.keyword":"owner_match"}},{"match":{"op.keyword":"ne"}}]}}}`

	ownerExemptionsDocs, err := elastic.ExecuteSearchQuery([]string{elastic.EXCEPTIONS_INDEX}, ownerExemptionsQuery)
	if err != nil {
		return
	}

	logger.Print(logger.INFO, "Owner exclusion exceptions", []string{resourceContext.TenantID}, ownerExemptionsDocs)

	for _, ownerExemptionsDoc := range ownerExemptionsDocs {
		key, _ := ownerExemptionsDoc["key"].(string)
		values, _ := ownerExemptionsDoc["values"].([]any)

		for _, v := range values {
			if val, ok := v.(string); ok {
				existingExceptions, _ := resourceContext.GetOwnerExceptions(key)
				resourceContext.SetOwnerExceptions(key, append(existingExceptions, val))
			}
		}
	}

}

func GetAllOwnerInclusionsForTenant(resourceContext *ResourceContext) {
	ownerInclusionsQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"type.keyword":"owner_match"}},{"match":{"op.keyword":"eq"}}]}}}`

	ownerInclusionsDocs, err := elastic.ExecuteSearchQuery([]string{elastic.EXCEPTIONS_INDEX}, ownerInclusionsQuery)
	if err != nil {
		return
	}

	logger.Print(logger.INFO, "Owner inclusion exceptions", []string{resourceContext.TenantID}, ownerInclusionsDocs)

	for _, ownerInclusionsDoc := range ownerInclusionsDocs {
		key, _ := ownerInclusionsDoc["key"].(string)
		values, _ := ownerInclusionsDoc["values"].([]any)

		for _, v := range values {
			if val, ok := v.(string); ok {
				existingInclusions, _ := resourceContext.GetOwnerInclusions(key)
				resourceContext.SetOwnerInclusions(key, append(existingInclusions, val))
			}
		}
	}

}

func GetAllTypoExceptionsForTenant(resourceContext *ResourceContext) {

	typoExemptionsQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"type.keyword":"typo"}},{"match":{"op.keyword":"eq"}}]}}}`

	typoExemptionsDocs, err := elastic.ExecuteSearchQuery([]string{elastic.EXCEPTIONS_INDEX}, typoExemptionsQuery)
	if err != nil {
		return
	}

	logger.Print(logger.INFO, "Typo exceptions", []string{resourceContext.TenantID}, typoExemptionsDocs)

	for _, typoExemptionsDoc := range typoExemptionsDocs {

		key, _ := typoExemptionsDoc["key"].(string)
		values, _ := typoExemptionsDoc["values"].([]any)

		for _, v := range values {
			if val, ok := v.(string); ok {
				existingExceptions, _ := resourceContext.GetTypoExceptions(key)
				resourceContext.SetTypoExceptions(key, append(existingExceptions, val))
			}
		}
	}

}

func getPrimaryUserSource(tenantID string) (primarySource string) {

	primarySourceQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}}]}}}`

	primarySourceDocs, err := elastic.ExecuteSearchQuery([]string{elastic.COMPANY_METADATA_INDEX}, primarySourceQuery)
	if err != nil {
		return
	}

	if len(primarySourceDocs) == 1 {
		primarySource, _ = primarySourceDocs[0]["userPrimarySource"].(string)
	}

	return
}

func PopulateExampleNames(tenantID string, exampleNames map[string]bool) {

	txtLookupQuery := `{"query":{"bool":{"filter":[{"term":{"tenantId":"` + tenantID + `"}}],"must_not":[{"wildcard":{"text":"*format*"}},{"wildcard":{"text":"*ticket*"}},{"wildcard":{"text":"*system*"}}]}},"aggs":{"hasName_true":{"filter":{"term":{"hasName":true}},"aggs":{"top_hasName_true":{"top_hits":{"size":100,"_source":["text"]}}}},"hasName_false":{"filter":{"term":{"hasName":false}},"aggs":{"top_hasName_false":{"top_hits":{"size":100,"_source":["text"]}}}}},"size":0}`
	aggsResp, err := elastic.ExecuteSearchForAggregation([]string{elastic.TEXT_LOOKUP_INDEX}, txtLookupQuery)
	if err == nil {
		jsonData, err := json.Marshal(aggsResp)
		if err != nil {
			return
		}

		var textLookUpAgsResp TextExampleAggs
		if err := json.Unmarshal(jsonData, &textLookUpAgsResp); err != nil {
			return
		}

		for _, bucket := range textLookUpAgsResp.HasNameTrue.TopHasNameTrue.Hits.Hits {
			text := bucket.Source.Text
			if len(strings.Split(text, " ")) < 4 {
				exampleNames[bucket.Source.Text] = true
			}

			if len(exampleNames) > 6 {
				break
			}
		}

		for _, bucket := range textLookUpAgsResp.HasNameFalse.TopHasNameFalse.Hits.Hits {
			text := bucket.Source.Text
			if len(strings.Split(text, " ")) < 4 {
				exampleNames[bucket.Source.Text] = false
			}

			if len(exampleNames) > 8 {
				break
			}
		}
	}
}

func GetAllOwnerEmailNamesForTenant(resourceContext *ResourceContext) {
	ownerEmailNameQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"type.keyword":"email_name_match"}},{"match":{"op.keyword":"eq"}}]}}}`

	ownerEmailNameDocs, err := elastic.ExecuteSearchQuery([]string{elastic.EXCEPTIONS_INDEX}, ownerEmailNameQuery)
	if err != nil {
		return
	}

	logger.Print(logger.INFO, "Owner email name match exceptions", []string{resourceContext.TenantID}, ownerEmailNameDocs)

	for _, ownerEmailNameDoc := range ownerEmailNameDocs {
		key, _ := ownerEmailNameDoc["key"].(string)
		values, _ := ownerEmailNameDoc["values"].([]any)

		for _, v := range values {
			if val, ok := v.(string); ok {
				resourceContext.SetOwnerEmailName(key, val)
			}
		}
	}
}

func GetAllEmailDerivationExceptionsForTenant(resourceContext *ResourceContext) {
	derivedEmailExemptionsQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"type.keyword":"email_derivation"}},{"match":{"op.keyword":"ne"}}]}}}`

	derivedEmailExemptionsDocs, err := elastic.ExecuteSearchQuery([]string{elastic.EXCEPTIONS_INDEX}, derivedEmailExemptionsQuery)
	if err != nil {
		return
	}

	logger.Print(logger.INFO, "Derived Email exclusions", []string{resourceContext.TenantID}, derivedEmailExemptionsDocs)

	for _, derivedEmailExemptionsDoc := range derivedEmailExemptionsDocs {

		key, _ := derivedEmailExemptionsDoc["key"].(string)
		values, _ := derivedEmailExemptionsDoc["values"].([]any)

		excludedEmails := make([]string, 0)

		for _, v := range values {
			if val, ok := v.(string); ok {
				excludedEmails = append(excludedEmails, val)
			}
		}

		if len(excludedEmails) > 0 {
			resourceContext.SetDerivedEmailExclusions(key, excludedEmails)
		}
	}
}

func GetAllEmailDerivationInclusionsForTenant(resourceContext *ResourceContext) {
	derivedEmailInclusionsQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"type.keyword":"email_derivation"}},{"match":{"op.keyword":"eq"}}]}}}`

	derivedEmailInclusionsDocs, err := elastic.ExecuteSearchQuery([]string{elastic.EXCEPTIONS_INDEX}, derivedEmailInclusionsQuery)
	if err != nil {
		return
	}

	logger.Print(logger.INFO, "Derived Email inclusions", []string{resourceContext.TenantID}, derivedEmailInclusionsDocs)

	for _, derivedEmailInclusionsDoc := range derivedEmailInclusionsDocs {

		key, _ := derivedEmailInclusionsDoc["key"].(string)
		values, _ := derivedEmailInclusionsDoc["values"].([]any)

		includedEmails := make([]string, 0)

		for _, v := range values {
			if val, ok := v.(string); ok {
				includedEmails = append(includedEmails, val)
			}
		}

		if len(includedEmails) > 0 {
			resourceContext.SetDerivedEmailInclusions(key, includedEmails)
		}
	}
}

func GetAllChildToPrimaryEmailInclusionsForTenant(resourceContext *ResourceContext) {
	ownerInclusionsQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"type.keyword":"parent_child_email"}},{"match":{"op.keyword":"eq"}}]}}}`

	childParentInclusionsDocs, err := elastic.ExecuteSearchQuery([]string{elastic.EXCEPTIONS_INDEX}, ownerInclusionsQuery)
	if err != nil {
		return
	}

	logger.Print(logger.INFO, "Parent Child inclusion exceptions", []string{resourceContext.TenantID}, childParentInclusionsDocs)

	for _, childParentInclusionsDoc := range childParentInclusionsDocs {
		key, _ := childParentInclusionsDoc["key"].(string)
		values, _ := childParentInclusionsDoc["values"].([]any)

		for _, v := range values {
			if val, ok := v.(string); ok {
				resourceContext.SetChildParentInclusions(key, val)
			}
		}
	}

}
