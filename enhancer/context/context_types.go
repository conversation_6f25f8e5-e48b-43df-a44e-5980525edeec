package context

import (
	"sync"

	"github.com/precize/transport"
)

const (
	COST_OWNER      = "cost"
	OPS_OWNER       = "operations"
	SECURITY_OWNER  = "security"
	DEFINED_OWNER   = "defined"
	DERIVED_OWNER   = "derived"
	INHERITED_OWNER = "inherited"
	CODE_OWNER      = "code"

	PROD_ENV    = "Production"
	STAGING_ENV = "QA/Staging"
	DEV_ENV     = "Development"
	SANDBOX_ENV = "Sandbox"

	SERVER_SOFTWARE    = "HTTP Server"
	MONGO_SOFTWARE     = "MongoDB"
	ELASTIC_SOFTWARE   = "ElasticSearch"
	POSTGRES_SOFTWARE  = "PostgreSQL"
	SQL_SOFTWARE       = "SQL"
	ORACLE_SOFTWARE    = "Oracle"
	REDIS_SOFTWARE     = "Redis"
	CASSANDRA_SOFTWARE = "Cassandra"
	NEO4J_SOFTWARE     = "Neo4j"
	VPN_SOFTWARE       = "VPN"
	NAT_SOFTWARE       = "NAT"
	NGINX_SOFTWARE     = "NGINX"
	NODEJS_SOFTWARE    = "NodeJs"

	TERRAFORM_DEPLOYMENT            = "Terraform"
	HELM_DEPLOYMENT                 = "Helm"
	KUBERNETES_DEPLOYMENT           = "Kubernetes"
	CFT_DEPLOYMENT                  = "AWS CloudFormation"
	AZDO_TF_DEPLOYMENT              = "Azure DevOps (Terraform)"
	AZDO_DEPLOYMENT                 = "Azure DevOps"
	ARGO_DEPLOYMENT                 = "Argo CD"
	ARGO_KUBERNETES_DEPLOYMENT      = "Argo CD (Kubernetes)"
	CLI_DEPLOYMENT                  = "CLI"
	CONSOLE_DEPLOYMENT              = "Console"
	JENKINS_DEPLOYMENT              = "Jenkins"
	GITHUB_DEPLOYMENT               = "GitHub"
	GITLAB_DEPLOYMENT               = "GitLab"
	BITBUCKET_DEPLOYMENT            = "Bitbucket"
	DOCKER_DEPLOYMENT               = "Docker"
	AWS_DEPLOYMENT                  = "AWS Service"
	GCP_DEPLOYMENT                  = "GCP Service"
	APPLICATION_DEPLOYMENT          = "Application"
	INFRASTRUCTURE_TOOLS_DEPLOYMENT = "Infrastructure Tools"

	JENKINS_APP   = "Jenkins"
	ARGO_APP      = "Argo CD"
	TERRAFORM_APP = "Terraform"
	CICD_APP      = "CI/CD"
	GITHUB_APP    = "GitHub"
	GITLAB_APP    = "GitLab"
	BITBUCKET_APP = "Bitbucket"

	HIPAA_COMPLIANCE  = "HIPAA"
	GDPR_COMPLIANCE   = "GDPR"
	PCIDSS_COMPLIANCE = "PCI DSS"
	NIST_COMPLIANCE   = "NIST"
	SOX_COMPLIANCE    = "SOX"
	FISMA_COMPLIANCE  = "FISMA"
	ISO_COMPLIANCE    = "ISO"
	FERPA_COMPLIANCE  = "FERPA"
	GLBA_COMPLIANCE   = "GLBA"
	CJIS_COMPLIANCE   = "CJIS"

	CONFIDENTIAL_SENSITIVITY = "Confidential"
	RESTRICTED_SENSITIVITY   = "Restricted"
	PUBLIC_SENSITIVITY       = "Public"
	INTERNAL_SENSITIVITY     = "Internal"
	PII_SENSITIVITY          = "PII"
	PCI_SENSITIVITY          = "PCI"
	PHI_SENSITIVITY          = "PHI"

	AWS_SDK_JAVA_USER_AGENT           = "AWS SDK Java"
	AWS_SDK_GO_USER_AGENT             = "AWS SDK Go"
	AWS_CLI_USER_AGENT                = "AWS CLI"
	AWS_BOTO3_USER_AGENT              = "AWS Boto3"
	AWS_SERVICE_USER_AGENT            = "AWS Service"
	CLOUDFORMATION_USER_AGENT         = "AWS CloudFormation"
	KUBERNETES_DEPLOYMENT_USER_AGENT  = "Kubernetes"
	TERRAFORM_USER_AGENT              = "Terraform"
	GOLANG_APP_USER_AGENT             = "Golang App"
	JAVA_SDK_USER_AGENT               = "Java SDK"
	MOZILLA_USER_AGENT                = "Mozilla"
	CLOUDWATCH_AGENT_USER_AGENT       = "CloudWatch Agent"
	EVENT_BRIDGE_SCHEDULER_USER_AGENT = "Amazon EventBridge Scheduler"
	S3_CONSOLE_USER_AGENT             = "S3 Console"
	AWS_CONSOLE_USER_AGENT            = "AWS Console"

	ENGINEERING_TEAM        = "Engineering"
	DEVOPS_TEAM             = "Devops"
	DEVELOPMENT_TEAM        = "Development Team"
	OPERATIONS_TEAM         = "Operations"
	PRODUCT_MANAGEMENT_TEAM = "Product Management"
	DESIGN_UXUI_TEAM        = "Design (UX/UI)"
	QA_TEAM                 = "QA"
	SECURITY_TEAM           = "Security"
	SALES_TEAM              = "Sales"
	MARKETING_TEAM          = "Marketing"
	CUSTOMER_SUPPORT_TEAM   = "Customer Support/Success"
	HR_TEAM                 = "HR"
	FINANCE_TEAM            = "Finance/Billing"
	COST_TEAM               = "Cost Team"
	DATA_TEAM               = "Data Team"
	SRE_TEAM                = "SRE"
	PLATFORM_TEAM           = "Platform Team"
	INFRA_TEAM              = "Infra Team"
	NETWORKING_TEAM         = "Networking"
	SOLUTIONS_TEAM          = "Solutions Team"
	COMPLIANCE_TEAM         = "Compliance"
	GOVERNANCE_TEAM         = "Governance"

	TAG_USERTYPE_PREFIX            = "Tag: "
	TAG_ENVTYPE_PREFIX             = "Tag: "
	TAG_APPTYPE_PREFIX             = "Tag: "
	TAG_SOFTWARETYPE_PREFIX        = "Tag: "
	TAG_DEPLOYMENTTYPE_PREFIX      = "Tag: "
	TAG_COMPLIANCETYPE_PREFIX      = "Tag: "
	TAG_SENSITIVITYTYPE_PREFIX     = "Tag: "
	TAG_COSTCENTER_PREFIX          = "Tag: "
	TAG_TEAMTYPE_PREFIX            = "Tag: "
	NAME_ONLY_PREFIX               = "n:"
	EX_EMPLOYEE_PREFIX             = "[X] "
	INVALID_EMPLOYEE_PREFIX        = "[I] "
	IAM_USER_SUFFIX                = " <IAM User>"
	IAM_ROLE_SUFFIX                = " <IAM Role>"
	APP_USER_SUFFIX                = " <Application>"
	SERVICEACCOUNT_USER_SUFFIX     = " <Service Account>"
	SERVICENAME_USER_SUFFIX        = " <ServiceName User>"
	RESOURCE_NAME_TEMP_USER_SUFFIX = " <Resource Name>"
	AWSSERVICE_USER_SUFFIX         = " <AWS Service>"
	ACCOUNT_USER_SUFFIX            = " <AWS Account>"
	INCLUDED_USER_SUFFIX           = " <Included User>"
	EXT_KEYWORD                    = "#ext#"
	LIVE_MICROSOFT_KEYWORD         = "live.com#"
	MAIL_MICROSOFT_KEYWORD         = "mail#"

	INTERNAL_PREFIX       = "PrecizeInternal:"
	COMMIT_FILE_SUBSTRING = "commitFile:"

	INVALID_EMAIL_ERROR   = "Invalid Email"
	NO_EMAIL_FORMAT_ERROR = "No email format"
)

// Gets populated at resource context initialization and on any additional finds
var (
	globalValuesMutex                   sync.Mutex
	defaultAppValues, defaultTeamValues []string
	appValues                           = make(map[string][]string)
	teamValues                          = make(map[string][]string)
	aiDetectedApps                      = make(map[string]string)
	aiRejectedTeamValues                = make(map[string]struct{})
)

func init() {
	teamValues = map[string][]string{
		// Common Teams
		ENGINEERING_TEAM:        {`engineering`},
		DEVOPS_TEAM:             {`dev[-_\s]*ops`},
		OPERATIONS_TEAM:         {`operations`, `\bops\b`},
		PRODUCT_MANAGEMENT_TEAM: {`product[-_\s]*management`},
		SECURITY_TEAM:           {`security`, `info[-_\s]*sec`},
		SALES_TEAM:              {`\bsales\b`},
		MARKETING_TEAM:          {`marketing`},
		CUSTOMER_SUPPORT_TEAM:   {`customer[-_\s]*support`, `customer[-_\s]*success`},
		HR_TEAM:                 {`human[-_\s]*resources`, `\bhr\b`},
		FINANCE_TEAM:            {`finance`, `accounting`, `billing`},
		COST_TEAM:               {`cost[-_\s]*optimization`, `cost[-_\s]*team`},
		DATA_TEAM:               {`data[-_\s]*team`, `data[-_\s]*engineering`, `data[-_\s]*analytics`},
		SRE_TEAM:                {`\bsre\b`, `site[-_\s]*reliability[-_\s]*engineering`},
		PLATFORM_TEAM:           {`platform[-_\s]*engineering`, `platform[-_\s]*team`},
		INFRA_TEAM:              {`infrastructure`, `\binfra\b`},
		NETWORKING_TEAM:         {`networking`},
		SOLUTIONS_TEAM:          {`solutions`},
		COMPLIANCE_TEAM:         {`compliance`},
		GOVERNANCE_TEAM:         {`governance`},
		// DEVELOPMENT_TEAM:        {`development`},
		// DESIGN_UXUI_TEAM:        {`design`, `\bux\b`, `\bui\b`, `user[-_\s]*interface`, `user[-_\s]*experience`},
		// QA_TEAM:                 {`quality[-_\s]*assurance`, `testing`, `\bqa\b`},
	}

	appValues = map[string][]string{
		// Common Apps
		JENKINS_APP:              {`jenkins`},
		ARGO_APP:                 {`argo[-_\s]*cd`},
		TERRAFORM_APP:            {`terraform`, `\btf\b`, `terragrunt`, `azdotf`},
		CICD_APP:                 {`\bcicd\b`},
		GITHUB_APP:               {`github`},
		GITLAB_APP:               {`gitlab`},
		BITBUCKET_APP:            {`bitbucket`},
		"Wiz":                    {`\bwiz\b`},
		"Orca":                   {`\borca\b`},
		"Palo Alto Prisma Cloud": {`palo[-_\s]*alto`, `prisma[-_\s]*cloud`},
		"Lacework":               {`lacework`},
		"Qualys":                 {`qualys`},
		"JFrog":                  {`jfrog`},
		"Okta":                   {`\bokta\b`},
		"Ping Identity":          {`ping[-_\s]*identity`},
		"ELK":                    {`\belk\b`},
		"Logstash":               {`logstash`},
		"Kibana":                 {`kibana`},
		"Splunk":                 {`splunk`},
		"Confluence":             {`confluence`},
		"Jira":                   {`\bjira\b`},
		"Akeyless":               {`akeyless`},
	}
}

var OwnerTagKeys = map[string]int{
	"owner": 1, "email": 2, "creator": 3, "developer": 4, "user": 5, "contact": 6, "approver": 7, "owners": 8, "admin": 9, "support": 10,
}

var OwnerTagKeysRegex = map[string]int{
	`created[-_\s]*by`: 1, `started[-_\s]*by`: 2, `developed[-_\s]*by`: 3, `modified[-_\s]*by`: 4, `approver[-_\s]*name`: 5, `approved[-_\s]*by`: 6,
}

var EnvTagKeys = map[string]int{
	"env": 1, "environment": 2, "stage": 3, "category": 4,
}

var SoftwareTagKeys = map[string]int{
	"app": 1, "application": 2, "software": 3,
}

var AppTagKeys = map[string]int{
	"app": 1, "application": 2,
}

var CostCenterTagKeys = map[string]int{
	`cost[-_\s]*center`: 1, `business[-_\s]*unit`: 2, `cost[-_\s]*bu`: 3, `^bu$`: 4,
}

var teamTagKeys = map[string]int{
	`team`: 1,
}

var RepoNameTagKeys = map[string]int{
	"git-repo": 1, "gitrepo": 2, "git_repo": 3, "repo": 4, "repository": 5, "repo-name": 6, "reponame": 7,
}

var RepoNameTagKeysRegex = map[string]int{
	`(?i)git[-_\s]*(repo|repository)`: 1, `(?i)(repo|repository)[-_\s]*name`: 2, `(?i)(repo|repository)[-_\s]*url`: 3, `(?i)source[-_\s]*(repo|repository)`: 4,
}

var FileNameTagKeys = map[string]int{
	"filename": 1, "file-name": 2, "gitfilename": 3, "git-file-name": 4, "file": 5, "gitfile": 6,
}

var FileNameTagKeysRegex = map[string]int{
	`(?i)git[-_\s]*file[-_\s]*name`: 1, `(?i)file[-_\s]*name`: 2, `(?i)source[-_\s]*file`: 3, `(?i)code[-_\s]*file`: 4, `(?i)repo[-_\s]*file`: 5, `(?i)git[-_\s]*file`: 6,
}

var CommitIDTagKeys = map[string]int{
	"commit": 1, "commit-id": 2, "commit_hash": 3, "commit-hash": 4, "gitcommit": 5,
}

var CommitIDTagKeysRegex = map[string]int{
	`(?i)commit[-_\s]*id`: 1, `(?i)commit[-_\s]*hash`: 2, `(?i)git[-_\s]*commit`: 3, `(?i)commit[-_\s]*ref`: 4,
}

var envValues = map[string][]string{
	STAGING_ENV: {"test", "qa", "stage", "staging", `\bstg\b`, `pre[-_\s]*prod`, `non[-_\s]*prod`},
	PROD_ENV:    {"\bprd\b", "prod", "production"},
	DEV_ENV:     {"dev", "poc"},
	SANDBOX_ENV: {"sandbox"},
}

var notMatchValues = map[string][]string{
	"test": {"latest", "attest", "contest", "pentest", "brightest", "detest", "protest", "testosterone", "fastest", "bretttest"},
	"prod": {`pre[-_\s]*prod`, `non[-_\s]*prod`, "producer", "productive", "product"},
	"dev":  {"device"},
}

var softwareValues = map[string][]string{
	MONGO_SOFTWARE:                     {`mongo`},
	ELASTIC_SOFTWARE:                   {`elastic[-_\s]*search`},
	POSTGRES_SOFTWARE:                  {`postgres`},
	SQL_SOFTWARE:                       {`mysql`},
	"VPN":                              {`\bvpn\b`},
	"NAT":                              {`\bnat\b`},
	"Nginx":                            {`nginx`},
	"Nodejs":                           {`node[-_\s]*js`},
	"Redis":                            {`redis`},
	"Cassandra":                        {`cassandra`},
	"DynamoDB":                         {`dynamodb`},
	"CouchDB":                          {`couchdb`},
	"SQLite":                           {`sqlite`},
	"InfluxDB":                         {`influxdb`},
	"Neo4j":                            {`neo4j`},
	"ClickHouse":                       {`clickhouse`},
	"MariaDB":                          {`mariadb`},
	"RocksDB":                          {`rocksdb`},
	"Apache Kafka":                     {`kafka`},
	"RabbitMQ":                         {`rabbitmq`},
	"NATS":                             {`\bnats\b`},
	"MQTT":                             {`\bmqtt\b`},
	"ZeroMQ":                           {`zeromq`},
	"Apache Pulsar":                    {`pulsar`},
	"Docker":                           {`docker`},
	"containerd":                       {`containerd`},
	"Kubernetes":                       {`kubernetes`},
	"CRI-O":                            {`cri[-_\s]*o`},
	"Podman":                           {`podman`},
	"Helm":                             {`helm`},
	"Istio":                            {`istio`},
	"Linkerd":                          {`linkerd`},
	"OpenShift":                        {`openshift`},
	"kube-proxy":                       {`kube-proxy`},
	"coredns":                          {`coredns`},
	"etcd":                             {`\betcd\b`},
	"Python":                           {`python`},
	"Java (JRE/JDK)":                   {`\bjava\b`},
	"Golang":                           {`\bgo\b`},
	"Rust":                             {`\brust\b`},
	"Ruby":                             {`\bruby\b`},
	"Gradle":                           {`gradle`},
	"Maven":                            {`maven`},
	"sbt":                              {`sbt`},
	"Keycloak":                         {`keycloak`},
	"Dex":                              {`\bdex\b`},
	"HashiCorp Vault":                  {`hashicorp[-_\s]*vault`},
	"Grafana":                          {`grafana`},
	"Loki":                             {`\bloki\b`},
	"Fluentd":                          {`fluentd`},
	"Fluent Bit":                       {`fluent[-_\s]*bit`},
	"OpenTelemetry Collector":          {`opentelemetry`},
	"Jaeger":                           {`jaeger`},
	"Zipkin":                           {`zipkin`},
	"Logstash":                         {`logstash`},
	"Kibana":                           {`kibana`},
	"Envoy":                            {`envoy`},
	"HAProxy":                          {`haproxy`},
	"Traefik":                          {`traefik`},
	"Kong":                             {`kong`},
	"Ambassador":                       {`ambassador`},
	"Trivy":                            {`trivy`},
	"Clair":                            {`clair`},
	"Anchore Engine":                   {`anchore[-_\s]*engine`},
	"Aqua":                             {`aqua`},
	"Snyk CLI":                         {`snyk[-_\s]*cli`},
	"Falco":                            {`falco`},
	"OpenSCAP":                         {`openscap`},
	"Gunicorn":                         {`gunicorn`},
	"uWSGI":                            {`uwsgi`},
	"Fastify":                          {`fastify`},
	"Django":                           {`django`},
	"Flask":                            {`flask`},
	"Spring Boot":                      {`spring[-_\s]*boot`},
	"Celery":                           {`celery`},
	"Airflow":                          {`airflow`},
	"Temporal":                         {`temporal`},
	"Apache NiFi":                      {`apache nifi`},
	"Prefect":                          {`prefect`},
	"BullMQ":                           {`bullmq`},
	"Sidekiq":                          {`sidekiq`},
	"Quartz Scheduler":                 {`quartz scheduler`},
	"Memcached":                        {`memcached`},
	"Varnish":                          {`varnish`},
	"Ehcache":                          {`ehcache`},
	"Caffeine":                         {`caffeine`},
	"Cilium":                           {`cilium`},
	"Calico":                           {`calico`},
	"Flannel":                          {`flannel`},
	"kube-router":                      {`kube[-_\s]*router`},
	"TensorFlow Serving":               {`tensorflow`},
	"TorchServe":                       {`torch[-_\s]*serve`},
	"ONNX Runtime":                     {`onnx[-_\s]*runtime`},
	"HuggingFace Transformers library": {`huggingface`},
	"MLflow":                           {`mlflow`},
	"JUnit":                            {`junit`},
	"pytest":                           {`pytest`},
	"Mocha":                            {`mocha`},
	"Jasmine":                          {`jasmine`},
	"TestNG":                           {`testng`},
	"Cypress":                          {`cypress`},
	"Selenium":                         {`selenium`},
	"Playwright":                       {`playwright`},
}

var deploymentKeyOrValues = map[string][]string{
	TERRAFORM_DEPLOYMENT: {`terraform`, `\btf\b`, `terragrunt`, `azdotf`},
	CFT_DEPLOYMENT:       {"cloudformation", `\bcft\b`},
	KUBERNETES_DEPLOYMENT: {`kubernetes`, `\bk8\b`, `kubectl`, `kube[-_\s]*config`, `\bhelm\b`, `kubelet`,
		`kubernetes[-_\s]*java[-_\s]*client`, `googlecontainerengine`, `google[-_\s]*connect[-_\s]*gateway`,
		`google[-_\s]*gkehub[-_\s]*controllers`, `google[-_\s]*backup[-_\s]*for[-_\s]*gke`,
		`googlegkegatewaycontroller`, `eks\.amazonaws\.com`, `amazon[-_\s]*vpc[-_\s]*cni`, `elbv2\.k8s\.aws`,
		`eks[-_\s]*nodegroup\.amazonaws\.com`, `otelcol[-_\s]*contrib`},
	DOCKER_DEPLOYMENT:    {`docker`},
	AZDO_DEPLOYMENT:      {`\bazdo\b`, `azure[-_\s]*devops`, `azdotf`},
	ARGO_DEPLOYMENT:      {`\bargo(?:cd)?\b`},
	JENKINS_DEPLOYMENT:   {`jenkins`},
	GITHUB_DEPLOYMENT:    {`github`},
	GITLAB_DEPLOYMENT:    {`gitlab`},
	BITBUCKET_DEPLOYMENT: {`bitbucket`},
	CLI_DEPLOYMENT: {`aws[-_\s]*cli`, `command[-_\s]*line`, `azure[-_\s]*cli`, `\bgcloud\b`, `gsutil`,
		`google[-_\s]*cloud[-_\s]*sdk`, `apitools`, `curl`, `postmanruntime`, `firebasecli`},
	CONSOLE_DEPLOYMENT: {`aws[-_\s]*console`, `gcp[-_\s]*console`, `azure[-_\s]*portal`, `mozilla`, `chrome`,
		`safari`, `webkit`, `applewebkit`, `khtml`, `gecko`, `edg`, `s3console`, `firefox`, `opr`,
		`android.*cloudconsole`, `cloudcode[-_\s]*vscode`},
	AWS_DEPLOYMENT: {`boto3`, `aws[-_\s]*sdk`, `aiobotocore`, `lambda`, `awslambda[-_\s]*worker`,
		`\.amazonaws\.com`, `awsstepfunctions`, `amazoneventbridgescheduler`, `patchmanager`, `cwagent`,
		`aws[-_\s]*internal`, `aws[-_\s]*sdk[-_\s]*dotnet[-_\s]*coreclr`, `aws[-_\s]*sdk[-_\s]*nodejs`,
		`botocore`, `aws[-_\s]*fluent[-_\s]*bit[-_\s]*plugin`, `aws[-_\s]*sdk[-_\s]*go`,
		`securityhub\.amazonaws\.com`, `autoscaling\.amazonaws\.com`, `datazone\.amazonaws\.com`,
		`internetmonitor\.amazonaws\.com`, `application[-_\s]*insights\.amazonaws\.com`,
		`organizations\.amazonaws\.com`, `datasync\.amazonaws\.com`, `lambda\.amazonaws\.com`,
		`compute[-_\s]*optimizer\.amazonaws\.com`, `events\.amazonaws\.com`, `acm\.amazonaws\.com`,
		`aoss\.amazonaws\.com`, `member\.org\.stacksets\.cloudformation\.amazonaws\.com`,
		`vmie\.amazonaws\.com`, `osis\.amazonaws\.com`, `servicediscovery\.amazonaws\.com`,
		`stacksets\.cloudformation\.amazonaws\.com`, `aws[-_\s]*toolkit[-_\s]*for[-_\s]*jetbrains`},
	GCP_DEPLOYMENT: {`google[-_\s]*api[-_\s]*go[-_\s]*client`, `google[-_\s]*api[-_\s]*python[-_\s]*client`,
		`google[-_\s]*api[-_\s]*java[-_\s]*client`, `gcloud[-_\s]*golang`, `gcloud[-_\s]*python`, `gcloud[-_\s]*java`,
		`gcloud[-_\s]*node`, `google[-_\s]*deployment[-_\s]*manager`, `googlecloudworkflows`,
		`bigquery[-_\s]*data[-_\s]*transfer[-_\s]*service`, `cloud[-_\s]*composer`, `gce[-_\s]*managed[-_\s]*instance[-_\s]*group`,
		`gce[-_\s]*csi[-_\s]*driver`, `google[-_\s]*cloud[-_\s]*dataproc[-_\s]*server`, `stubby[-_\s]*client`,
		`boq[-_\s]*goa[-_\s]*gce`, `app[-_\s]*engine`, `gce[-_\s]*managed[-_\s]*instance[-_\s]*group[-_\s]*for[-_\s]*appengine`,
		`gce[-_\s]*managed[-_\s]*instance[-_\s]*group[-_\s]*for[-_\s]*dataflow`, `gce[-_\s]*managed[-_\s]*instance[-_\s]*group[-_\s]*for[-_\s]*tesseract`,
		`cloud[-_\s]*workflow[-_\s]*service`, `boq[-_\s]*cloud[-_\s]*services[-_\s]*platform[-_\s]*gcesync`,
		`velostrata`, `tesseract`, `google[-_\s]*hybrid[-_\s]*cloud[-_\s]*storage`, `bigstorefile`,
		`grpc[-_\s]*python`, `grpc[-_\s]*c`, `grpc[-_\s]*java[-_\s]*cronet`, `grpc[-_\s]*c\+\+`,
		`loaddata.*bigtable`, `transferservice`, `workflow[-_\s]*service`, `coral/netty`,
		`boq[-_\s]*cloud[-_\s]*aiplatform[-_\s]*pipeline`, `gl[-_\s]*cpp.*dremel`, `python[-_\s]*urllib3`},
	APPLICATION_DEPLOYMENT: {`golang[-_\s]*app`, `go[-_\s]*http[-_\s]*client`, `grpc[-_\s]*go`, `java[-_\s]*sdk`,
		`spring`, `python[-_\s]*app`, `tornado`, `airflow`, `nodejs[-_\s]*app`, `node[-_\s]*fetch`,
		`databricks[-_\s]*api`, `axios`},
	INFRASTRUCTURE_TOOLS_DEPLOYMENT: {`packer`, `blob`, `\bm/[ef]\b`, `\bapn\b`, `\bfog\b`},
}

var userAgentKeyOrValues = map[string][]string{
	AWS_SDK_JAVA_USER_AGENT:           {`\baws-sdk-java\b`},
	AWS_SDK_GO_USER_AGENT:             {`\baws-sdk-go\b`},
	AWS_CLI_USER_AGENT:                {`\baws-cli\b`},
	AWS_BOTO3_USER_AGENT:              {`\bboto3\b`},
	AWS_SERVICE_USER_AGENT:            {`amazonaws\.com`, `\baws\s*service\b`},
	CLOUDFORMATION_USER_AGENT:         {`\bcloudformation\b`, "cloudformation", `\bcft\b`},
	KUBERNETES_DEPLOYMENT_USER_AGENT:  {`kubernetes`, `\bk8\b`, `kubectl`, `kube[-_\s]*config`, `kube[-_\s]*config`, `\bhelm\b`},
	TERRAFORM_USER_AGENT:              {`\bterraform\b`, `\bterragrunt\b`},
	GOLANG_APP_USER_AGENT:             {`\bgolang\s*app\b`, `\bgo\b`},
	JAVA_SDK_USER_AGENT:               {`\bjava\s*sdk\b`, `\bjersey\b`},
	MOZILLA_USER_AGENT:                {`\bmozilla\b`},
	CLOUDWATCH_AGENT_USER_AGENT:       {`\bcwagent\b`, `\bcfntools\b`},
	EVENT_BRIDGE_SCHEDULER_USER_AGENT: {`\bamazonEventBridgeScheduler\b`},
	S3_CONSOLE_USER_AGENT:             {`\bs3console\b`},
	AWS_CONSOLE_USER_AGENT:            {`\baws\s*console\b`},
}

var complianceKeyOrValues = map[string]string{
	HIPAA_COMPLIANCE:  `\bhipaa\b`,
	GDPR_COMPLIANCE:   `\bgdpr\b`,
	PCIDSS_COMPLIANCE: `pci[-_\s]*dss`,
	NIST_COMPLIANCE:   `\bnist\b`,
	SOX_COMPLIANCE:    `\bsox\b`,
	FISMA_COMPLIANCE:  `\bfisma\b`,
	ISO_COMPLIANCE:    `\biso\b`,
	FERPA_COMPLIANCE:  `\bferpa\b`,
	GLBA_COMPLIANCE:   `\bglba\b`,
	CJIS_COMPLIANCE:   `\bcjis\b`,
}

var sensitivityKeyOrValues = map[string]string{
	CONFIDENTIAL_SENSITIVITY: `confidential`,
	RESTRICTED_SENSITIVITY:   `restricted`,
	PUBLIC_SENSITIVITY:       `^public$`,
	INTERNAL_SENSITIVITY:     `^internal$`,
	PCI_SENSITIVITY:          `\bpci\b`,
	PII_SENSITIVITY:          `\bpii\b`,
	PHI_SENSITIVITY:          `\bphi\b`,
}

var portToSoftware = map[int]string{
	80:    SERVER_SOFTWARE,
	443:   SERVER_SOFTWARE,
	3306:  SQL_SOFTWARE, // MySQL
	1433:  SQL_SOFTWARE, // MsSQL
	9200:  ELASTIC_SOFTWARE,
	27017: MONGO_SOFTWARE,
	5432:  POSTGRES_SOFTWARE,
	1521:  ORACLE_SOFTWARE,
	6379:  REDIS_SOFTWARE,
	9042:  CASSANDRA_SOFTWARE,
	7687:  NEO4J_SOFTWARE,
}

var emailDomains = []string{"com", "co", "io", "net", "org", "biz", "gov"}

var descriptionTagKeys = map[string]int{
	"description": 1, "desc": 2,
}

var complianceEnumToValue = map[string]string{
	"HIPAA_COMPLIANCE":  "HIPAA",
	"GDPR_COMPLIANCE":   "GDPR",
	"PCIDSS_COMPLIANCE": "PCIDSS",
	"PCI_COMPLIANCE":    "PCI",
	"NIST_COMPLIANCE":   "NIST",
	"SOX_COMPLIANCE ":   "SOX",
	"FISMA_COMPLIANCE":  "FISMA",
	"ISO_COMPLIANCE":    "ISO",
	"FERPA_COMPLIANCE":  "FERPA",
	"GLBA_COMPLIANCE":   "GLBA",
	"CJIS_COMPLIANCE":   "CJIS",
	"PII_COMPLIANCE":    "PII",
}

type VerifyEmailResponse struct {
	transport.ServerResponseInfo
	Data map[string]string `json:"data"`
}

type VerifyEmailErrorResponse struct {
	transport.ServerResponseInfo
	Data string `json:"data"`
}

type VerifyEmailError struct {
	Code   string `json:"code"`
	Detail string `json:"detail"`
	Wait   string `json:"wait"`
}

var ExcludedDepartments = map[string]struct{}{
	"call center":                    {},
	"bpo":                            {},
	"support":                        {},
	"temporary":                      {},
	"contract":                       {},
	"sports group":                   {},
	"call center - monterrey":        {},
	"american college of cardiology": {},
	"human resources":                {},
	"inside sales - anup patnaik":    {},
	"finance and accounts":           {},
	"human resource":                 {},
	"financial crimes security23002": {},
	"fin ops 13091":                  {},
	"hr - tag":                       {},
	"financial operatns - opm 13090": {},
	"inside sales 31090":             {},
	"spencer’s gift":                 {},
	"f&o":                            {},
	"people talent 44020":            {},
	"salesforce practice":            {},
	"marketing 32010":                {},
	"corp apps finance 22000":        {},
	"travel desk":                    {},
	"call center - chsa/cobra 11095": {},
	"us recruitment":                 {},
	"custodial operations 13002":     {},
	"accounting":                     {},
	"people development 44030":       {},
	"transfer pricing":               {},
	"support practice-hr":            {},
	"call center - cary":             {},
	"delivery multiple":              {},
	"legal 43000":                    {},
	"test":                           {},
	"support services":               {},
	"internal - customer success":    {},
	"call center - manila":           {},
	"support services 14002":         {},
	"claims - non classic":           {},
	"american auto shield":           {},
	"call center - kochi":            {},
	"claims classic 12041":           {},
	"finance operations - 42015":     {},
	"delivery":                       {},
	"support function":               {},
	"strategic client relationships & advisory services": {},
	"corporate reporting 42014":                          {},
	"corporate accounting 42010":                         {},
	"executive 41000":                                    {},
	"claims team - lexington, ky":                        {},
	"marketing":                                          {},
	"fe credit":                                          {},
	"call center - raleigh":                              {},
	"up! app":                                            {},
	"property management":                                {},
	"pop service delivery 13070":                         {},
	"service support":                                    {},
	"internal audit 42020":                               {},
	"ares health systems":                                {},
	"call center - lexington":                            {},
	"medical solutions":                                  {},
	"delivery practice":                                  {},
	"call center - chesapeake":                           {},
	"boch family foundation":                             {},
	"people 44010":                                       {},
	"finance":                                            {},
	"sound exchange":                                     {},
	"customer service":                                   {},
	"management":                                         {},
	"external":                                           {},
	"bi & reporting":                                     {},
	"workplace":                                          {},
	"demand":                                             {},
	"carrier supply":                                     {},
	"people ops":                                         {},
	"general":                                            {},
	"finance & legal":                                    {},
	"business insights & enablement":                     {},
	"commercial":                                         {},
	"ods legal":                                          {},
	"people":                                             {},
	"business apac":                                      {},
	"business ops":                                       {},
	"content media":                                      {},
	"noc team":                                           {},
	"reporting":                                          {},
	"revenue":                                            {},
	"total rewards":                                      {},
	"sales":                                              {},
	"senior revenue accountant":                          {},
	"accountant":                                         {},
}

var techKeywords = []string{
	"engineer", "developer", "devops", "operations", "architect", "sre",
	"infra", "infrastructure", "technologist", "platform", "cloud", "data",
	"system", "systems", "network", "it", "cyber", "security", "product",
}
