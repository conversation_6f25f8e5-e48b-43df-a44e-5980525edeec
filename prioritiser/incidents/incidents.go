package incidents

import (
	"bytes"
	"encoding/json"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/email"
	"github.com/precize/enhancer/context"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

const (
	MAX_RECORDS = 4000
)

func ReprioritiseIncidents(tenantID, lastCollectedAt, serviceID string) {

	defer func() {
		if r := recover(); r != nil {
			logger.Print(logger.ERROR, "Panic occured", r)
			email.SendPanicEmail("prioritiser")
		}

		logger.LogEmailProcessor("", true)
	}()

	var (
		criteria        Criteria
		searchAfter     any
		counts          = make(map[string]map[string]int)
		bulkUpdateQuery string
		currentCount    int
		crsIdToDocMap   = make(map[string]common.CloudResourceStoreDoc)
	)

	if len(serviceID) <= 0 {
		serviceID = common.GetServiceID(tenantID, lastCollectedAt)
		if len(serviceID) <= 0 {
			logger.Print(logger.INFO, "Invalid collectedAt at or tenantId", tenantID, lastCollectedAt)
			return
		}
	}

	criticalHeroStats := make(map[string]any)

	if len(lastCollectedAt) > 0 && len(serviceID) > 0 {

		var (
			buf                 bytes.Buffer
			heroStatsAPIRequest = common.HeroStatsAPIReq{
				AccountIds: []string{},
			}
		)

		err := json.NewEncoder(&buf).Encode(heroStatsAPIRequest)
		if err != nil {
			logger.Print(logger.ERROR, "Got error encoding hero stats request body", []string{tenantID}, err)
			return
		}
		serviceName := common.IdStrToCspStrMap[serviceID]
		resp, err := transport.SendRequestToServer("POST", "/precize/private/"+serviceName+"/"+tenantID+"/heroStats/"+lastCollectedAt, nil, &buf)
		if err != nil {
			return
		}

		var heroStatsResp common.HeroStatsAPIResponse

		if err = json.Unmarshal(resp, &heroStatsResp); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
			return
		}

		for _, heroStat := range heroStatsResp.Data {
			if heroStat.ResourceCount > 0 && heroStat.Severity == "high" {
				criticalHeroStats[heroStat.DbKey] = struct{}{}
			}
		}
	}

	incidentsQuery := `{"query":{"bool":{"must":[{"match":{"tenantId":"` + tenantID + `"}},{"terms":{"serviceId":["` + serviceID + `", "` + common.AI_VIEW_ID + `"]}}],"must_not":[{"match":{"stage.keyword":"ignore"}},{"match":{"status.keyword":"archived"}},{"match":{"type.keyword":"identity"}},{"match":{"type.keyword":"anomaly"}},{"match":{"status.keyword":"resolved"}}],"should":[]}}}`

	logger.Print(logger.INFO, "Starting incident prioritisation for tenant", []string{tenantID}, serviceID)

	for {

		var (
			crsIDs []string
			crsDoc common.CloudResourceStoreDoc
		)

		incidentDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_INCIDENTS_INDEX}, incidentsQuery, searchAfter)
		if err != nil {
			break
		}

		if len(incidentDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, incidentDoc := range incidentDocs {
			if crsId, ok := incidentDoc["crsId"].(string); ok && len(crsId) > 0 {
				crsIDs = append(crsIDs, crsId)
			}
		}

		crsQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"terms":{"id.keyword":["` + strings.Join(crsIDs, `","`) + `"]}}]}}}`

		crsDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, crsQuery)
		if err != nil {
			return
		}

		crsIDs = make([]string, 0)

		if len(crsDocs) > 0 {
			for _, doc := range crsDocs {
				var crsDoc common.CloudResourceStoreDoc
				b, err := json.Marshal(doc)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
					continue
				}

				if err = json.Unmarshal(b, &crsDoc); err != nil {
					logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
					continue
				}
				crsIdToDocMap[crsDoc.ID] = crsDoc
			}

		}

		for docID, incidentDoc := range incidentDocs {
			incidentDocBytes, err := json.Marshal(incidentDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
				continue
			}

			var incidentDataMap common.Incident
			if err = json.Unmarshal(incidentDocBytes, &incidentDataMap); err != nil {
				logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
				continue
			}

			if incidentDataMap.SourceRisk == common.INFORMATIONAL_RISK {
				continue
			}

			pData := PrioritisationData{
				SourceScore:        incidentDataMap.SourceScore,
				SourceRisk:         incidentDataMap.SourceRisk,
				PrecizeLikelihood:  likelihoodStringToNumber[common.VERY_UNLIKELY_LIKELIHOOD],
				PrecizeImpact:      impactStringToNumber[common.NEGLIGIBLE_IMPACT],
				IssueSeverityScore: incidentDataMap.IssueSeverityScore,
				IssueSeverity:      incidentDataMap.IssueSeverity,
				SourceLikelihood:   likelihoodStringToNumber[common.VERY_UNLIKELY_LIKELIHOOD],
				SourceImpact:       impactStringToNumber[common.NEGLIGIBLE_IMPACT],
				OpenToInternet:     false,
				TenantID:           incidentDataMap.TenantID,
				PriorityCriteria:   make(map[string]bool),
				IsResourceProd:     false,
				EntityType:         incidentDataMap.EntityType,
				SensitiveData:      false,
				criticalHeroStats:  criticalHeroStats,
			}

			if len(incidentDataMap.CrsID) <= 0 {

				switch incidentDataMap.Source {

				case common.ORCA_SOURCE:

					crsDoc, err = common.GetCRSDocFromNames(incidentDataMap.EntityID, incidentDataMap.EntityType, incidentDataMap.AccountName, incidentDataMap.TenantID)
					if err != nil {
						return
					}

				case common.DEFENDER_SOURCE, common.WIZ_SOURCE:

					crsDoc, err = common.GetCRSDocFromResourceID(incidentDataMap.EntityID, incidentDataMap.AccountID, incidentDataMap.TenantID)
					if err != nil {
						return
					}
				}
			} else {
				if cDoc, ok := crsIdToDocMap[incidentDataMap.CrsID]; ok {
					crsDoc = cDoc
				}
			}

			if len(crsDoc.ID) > 0 {
				incidentDataMap.CrsID = crsDoc.ID
				incidentDataMap.EntityID = crsDoc.EntityID
				incidentDataMap.ResourceName = crsDoc.ResourceName
				incidentDataMap.AccountID = crsDoc.AccountID
				incidentDataMap.Environment = crsDoc.Environment
				incidentDataMap.EntityType = crsDoc.EntityType
				incidentDataMap.Owner = crsDoc.Owner
				if len(crsDoc.Herostat) > 0 {
					pData.heroStats = append(pData.heroStats, crsDoc.Herostat...)
				}
			}

			switch incidentDataMap.Source {
			case common.ORCA_SOURCE, common.WIZ_SOURCE:
				if pData.IssueSeverityScore > 0 {
					pData.SourceLikelihood, pData.SourceImpact =
						GetLikelihoodAndImpactFromRiskScore(pData.IssueSeverityScore)
				} else {
					pData.SourceLikelihood, pData.SourceImpact =
						GetLikelihoodAndImpactFromRiskLevel(pData.IssueSeverity)
				}
			default:

				if pData.SourceRisk == common.NOTEVALUATED_RISK {
					pData.SourceRisk = pData.IssueSeverity
				}

				if pData.SourceScore > 0 {
					pData.SourceLikelihood, pData.SourceImpact =
						GetLikelihoodAndImpactFromRiskScore(pData.SourceScore)
				} else {
					pData.SourceLikelihood, pData.SourceImpact =
						GetLikelihoodAndImpactFromRiskLevel(pData.SourceRisk)
				}
			}

			pData.PrecizeImpact = pData.SourceImpact
			pData.PrecizeLikelihood = pData.SourceLikelihood

			if len(incidentDataMap.AdditionalData) > 0 {

				var additionalDataMap = make(map[string]any)

				if err = json.Unmarshal([]byte(incidentDataMap.AdditionalData), &additionalDataMap); err != nil {
					logger.Print(logger.ERROR, "Got error unmarshalling json additional data", []string{incidentDataMap.TenantID}, err)
					return
				}

				switch incidentDataMap.Source {
				case common.ORCA_SOURCE:
					if internetFacing, ok := additionalDataMap["isInternetFacing"].(bool); ok {
						pData.OpenToInternet = internetFacing
					}
				case common.DEFENDER_SOURCE:
					if riskFactors, ok := additionalDataMap["riskFactors"].([]any); ok {
						for _, riskFactor := range riskFactors {
							if rf, _ := riskFactor.(string); rf == "Exposure to the Internet" {
								pData.OpenToInternet = true
							} else if rf, _ := riskFactor.(string); rf == "Sensitive Data" {
								pData.SensitiveData = true
							}
						}
					}
				}
			}

			if _, ok := costRelatedIncidents[incidentDataMap.Issue]; ok {
				ReprioritiseCostIncidents(docID, counts, incidentDataMap, pData, crsDoc, &criteria, &bulkUpdateQuery, &currentCount, lastCollectedAt)
			} else {
				ReprioritiseResourceLevelIncidents(docID, counts, incidentDataMap, pData, crsDoc, &criteria, &bulkUpdateQuery, &currentCount, lastCollectedAt)
			}
			if currentCount > MAX_RECORDS {
				if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkUpdateQuery); err != nil {
					return
				}
				time.Sleep(time.Second * 1)

				logger.Print(logger.INFO, "Cloud Incident bulk API Successful for Incidents Reprioritisation for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
				currentCount = 0
				bulkUpdateQuery = ""
			}
		}
	}

	if currentCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkUpdateQuery); err != nil {
			return
		}
		time.Sleep(time.Second * 1)

		logger.Print(logger.INFO, "Cloud Incident bulk API Successful for Incidents Reprioritisation for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
	}

	logger.Print(logger.INFO, "Final counts", counts)
	logger.Print(logger.INFO, "Incident reprioritising completed successfully", []string{tenantID})

	logger.Print(logger.INFO, "Starting resource risk evaluation", []string{tenantID}, serviceID, lastCollectedAt)

	EvaluateResourcesRisk(tenantID, lastCollectedAt, serviceID, criticalHeroStats)

	logger.Print(logger.INFO, "Finished resource risk evaluation", []string{tenantID})

}

func ReprioritiseResourceLevelIncidents(incidentDocId string, counts map[string]map[string]int, incidentDataMap common.Incident, pData PrioritisationData, crsDoc common.CloudResourceStoreDoc, criteria *Criteria, bulkUpdateQuery *string, currentCount *int, lastCollectedAt string) (err error) {

	var (
		incidentUpdateMetadata, incidentUpdateDoc string
	)

	if len(crsDoc.EntityID) > 0 && !crsDoc.IsDeleted {

		pData.tenantValues, pData.accountValues, pData.resourceValues, err = fetchCriteriaValues(criteria, crsDoc, lastCollectedAt)
		if err != nil {
			return
		}

		if slices.Contains(crsDoc.Sensitivity, context.PII_SENSITIVITY) || slices.Contains(crsDoc.Sensitivity, context.PCI_SENSITIVITY) ||
			slices.Contains(crsDoc.Sensitivity, context.PHI_SENSITIVITY) || slices.Contains(crsDoc.Sensitivity, context.CONFIDENTIAL_SENSITIVITY) || slices.Contains(crsDoc.Sensitivity, context.RESTRICTED_SENSITIVITY) {
			pData.SensitiveData = true
		}

		if !pData.OpenToInternet {
			IsResourceOpenToInternet(&pData)
		}

		if pData.SensitiveData {
			if pData.OpenToInternet {
				SetImpactWithoutAvg(common.SEVERE_IMPACT, SENSITIVEDATA_OPENTOINTERNET_CRITERIA, &pData, true)
			} else {
				SetImpactWithoutAvg(common.SIGNIFICANT_IMPACT, SENSITIVEDATA_CRITERIA, &pData, true)
			}
		}

		ReprioritiseBasedOnEnv(&pData, crsDoc)

		if pData.OpenToInternet {
			if pData.IsResourceProd {
				SetLikelihoodWithoutAvg(common.VERY_LIKELY_LIKELIHOOD, OPENTOINTERNET_CRITERIA, &pData, true)
			} else {
				SetLikelihoodWithoutAvg(common.LIKELY_LIKELIHOOD, OPENTOINTERNET_CRITERIA, &pData, true)
			}
		}
		ReprioritiseBasedOnOwner(&pData, crsDoc)

		if pData.accountValues.resourceCount > 100 {
			ReprioritiseBasedOnActivity(&pData)
			ReprioritiseBasedOnAccount(&pData)
		}
		ReprioritiseBasedOnResource(&pData, crsDoc)

		likelihood := likelihoodNumberToString[float32(CustomRound(float64(pData.PrecizeLikelihood), 0.65))]
		impact := impactNumberToString[float32(CustomRound(float64(pData.PrecizeImpact), 0.65))]

		riskLevel, _ := GetRiskDetails(likelihood, impact)

		var (
			priorityCriteria       []string
			highImpactCriteria     []string
			lowImpactCriteria      []string
			highLikelihoodCriteria []string
			lowLikelihoodCriteria  []string
			highRiskCriteria       []string
			lowRiskCriteria        []string
		)

		riskLevel = ReprioritiseBasedOnFindings(&pData, riskLevel, incidentDataMap)
		riskLevel = ReprioritiseBasedOnIssue(&pData, riskLevel, incidentDataMap)
		riskLevel = ReprioritiseBasedOnStaticIssueConditions(&pData, riskLevel, incidentDataMap)

		if pData.SourceRisk == common.LOW_RISK {
			riskLevel = pData.SourceRisk
		}

		pData.PriorityCriteria = DeleteDuplicateCriteria(pData.PriorityCriteria)

		for criteria, inc := range pData.PriorityCriteria {
			if riskScoreMap[riskLevel] > riskScoreMap[incidentDataMap.SourceRisk] && inc {
				priorityCriteria = append(priorityCriteria, criteria)
			} else if riskScoreMap[riskLevel] < riskScoreMap[incidentDataMap.SourceRisk] && !inc {
				priorityCriteria = append(priorityCriteria, criteria)
			} else if riskScoreMap[riskLevel] == riskScoreMap[incidentDataMap.SourceRisk] || incidentDataMap.SourceRisk == common.NOTEVALUATED_RISK {
				priorityCriteria = append(priorityCriteria, criteria)
			}
		}

		// Categorize the criteria
		for _, criteria := range priorityCriteria {
			if criteriaInfo, exists := criteriaMap[criteria]; exists {
				if criteriaInfo.Type == "impact" {
					if criteriaInfo.Severity {
						highImpactCriteria = append(highImpactCriteria, criteria)
					} else {
						lowImpactCriteria = append(lowImpactCriteria, criteria)
					}
				} else if criteriaInfo.Type == "likelihood" {
					if criteriaInfo.Severity {
						highLikelihoodCriteria = append(highLikelihoodCriteria, criteria)
					} else {
						lowLikelihoodCriteria = append(lowLikelihoodCriteria, criteria)
					}
				} else if criteriaInfo.Type == "risk" {
					if criteriaInfo.Severity {
						highRiskCriteria = append(highRiskCriteria, criteria)
					} else {
						lowRiskCriteria = append(lowRiskCriteria, criteria)
					}
				}
			}
		}

		if incidentDataMap.SourceRisk == common.NOTEVALUATED_RISK {
			incidentDataMap.SeverityDiff = 2
		} else if riskScoreMap[riskLevel] == riskScoreMap[incidentDataMap.SourceRisk] {
			incidentDataMap.SeverityDiff = 0
		} else if riskScoreMap[riskLevel] > riskScoreMap[incidentDataMap.SourceRisk] {
			incidentDataMap.SeverityDiff = 1
		} else {
			incidentDataMap.SeverityDiff = -1
		}

		if len(highImpactCriteria) <= 0 && len(highLikelihoodCriteria) <= 0 && len(highRiskCriteria) <= 0 {
			if incidentDataMap.SeverityDiff == 1 {
				riskLevel = DecreaseRiskByOneLevel(riskLevel)
			} else if incidentDataMap.SeverityDiff == -1 {
				// Risk should not be change to critical if it is a non-prod resource
				if !(riskLevel == common.HIGH_RISK && !pData.IsResourceProd) {
					modifiedRiskLevel := IncreaseRiskByOneLevel(riskLevel)
					if modifiedRiskLevel != pData.SourceRisk {
						riskLevel = modifiedRiskLevel
					}
				}
			}

			if riskScoreMap[riskLevel] == riskScoreMap[incidentDataMap.SourceRisk] {
				incidentDataMap.SeverityDiff = 0
			}
		}

		if incidentDataMap.SeverityDiff == 0 {
			highImpactCriteria, highLikelihoodCriteria, lowImpactCriteria, lowLikelihoodCriteria, highRiskCriteria, lowRiskCriteria = []string{}, []string{}, []string{}, []string{}, []string{}, []string{}

			// Categorize the criteria
			for _, criteria := range priorityCriteria {
				if criteriaInfo, exists := criteriaMap[criteria]; exists {
					if criteriaInfo.Type == "impact" {
						if criteriaInfo.Severity {
							highImpactCriteria = append(highImpactCriteria, criteria)
						} else {
							lowImpactCriteria = append(lowImpactCriteria, criteria)
						}
					} else if criteriaInfo.Type == "likelihood" {
						if criteriaInfo.Severity {
							highLikelihoodCriteria = append(highLikelihoodCriteria, criteria)
						} else {
							lowLikelihoodCriteria = append(lowLikelihoodCriteria, criteria)
						}
					} else if criteriaInfo.Type == "risk" {
						if criteriaInfo.Severity {
							highRiskCriteria = append(highRiskCriteria, criteria)
						} else {
							lowRiskCriteria = append(lowRiskCriteria, criteria)
						}
					}
				}
			}
		}

		//Calculate Precize Score
		precizeScore := (pData.SourceLikelihood + pData.SourceImpact + (2 * pData.PrecizeImpact) + (2 * pData.PrecizeLikelihood) + (5 * riskScoreMap[riskLevel])) / 5.0

		priorityCriteria = []string{}
		incidentDataMap.PrecizeRisk = riskLevel

		// Generate Description
		description := GeneratePrecizeIncidentRepriorDesc(highImpactCriteria, lowImpactCriteria, highLikelihoodCriteria, lowLikelihoodCriteria,
			highRiskCriteria, lowRiskCriteria, incidentDataMap, pData)
		priorityCriteria = append(priorityCriteria, description)

		if _, ok := counts[incidentDataMap.SourceRisk]; !ok {
			counts[incidentDataMap.SourceRisk] = make(map[string]int)
		}

		counts[incidentDataMap.SourceRisk][riskLevel]++

		// Add escape characters if double quotes are present
		if len(incidentDataMap.AccountName) > 0 && (strings.Contains(incidentDataMap.AccountName, `"`) || strings.Contains(incidentDataMap.AccountName, `\`)) {
			incidentDataMap.AccountName = common.EscapeString(incidentDataMap.AccountName)
		}

		if len(incidentDataMap.ResourceName) > 0 && (strings.Contains(incidentDataMap.ResourceName, `"`) || strings.Contains(incidentDataMap.ResourceName, `\`)) {
			incidentDataMap.ResourceName = common.EscapeString(incidentDataMap.ResourceName)
		}

		incidentUpdateMetadata = `{"update": {"_id": "` + incidentDocId + `"}}`
		incidentUpdateDoc = `{"doc": {"precizeRisk": "` + riskLevel +
			`", "entityId":"` + incidentDataMap.EntityID +
			`", "entityType":"` + incidentDataMap.EntityType +
			`", "crsId":"` + incidentDataMap.CrsID +
			`", "owner":["` + strings.Join(incidentDataMap.Owner, "\",\"") +
			`"], "environment":["` + strings.Join(incidentDataMap.Environment, "\",\"") +
			`"], "resourceName":"` + incidentDataMap.ResourceName +
			`", "accountId":"` + incidentDataMap.AccountID +
			`", "accountName":"` + incidentDataMap.AccountName +
			`", "severityDiff":` + strconv.Itoa(incidentDataMap.SeverityDiff) +
			`, "precizeScore":` + fmt.Sprintf("%.2f", precizeScore) +
			`,"stage": "prioritised", "priorityCriteria":["` + strings.Join(priorityCriteria, "\",\"") +
			`"]}}`

	} else if len(incidentDataMap.CrsID) > 0 {
		// Resource not found
		incidentUpdateMetadata = `{"update": {"_id": "` + incidentDocId + `"}}`
		incidentUpdateDoc = `{"doc": {"stage": "notfound"}}`
	} else {
		// EntityType mapping not found
		return nil
	}

	*bulkUpdateQuery = *bulkUpdateQuery + incidentUpdateMetadata + "\n" + incidentUpdateDoc + "\n"
	*currentCount++

	return
}

func ReprioritiseCostIncidents(incidentDocId string, counts map[string]map[string]int, incidentDataMap common.Incident, pData PrioritisationData, crsDoc common.CloudResourceStoreDoc, criteria *Criteria, bulkUpdateQuery *string, currentCount *int, lastCollectedAt string) (err error) {

	var (
		incidentUpdateMetadata, incidentUpdateDoc string
	)

	if len(crsDoc.EntityID) > 0 && !crsDoc.IsDeleted {

		pData.tenantValues, pData.accountValues, pData.resourceValues, err = fetchCriteriaValues(criteria, crsDoc, lastCollectedAt)
		if err != nil {
			return
		}

		incidentDataMap.PrecizeRisk = pData.SourceRisk

		ReprioritiseBasedOnRelativeCost(&pData, crsDoc)

		ReprioritiseCostBasedOnEnv(&pData, crsDoc)

		ReprioritiseBasedOnCostCenter(&pData, crsDoc)

		var (
			riskLevel        = pData.PrecizeRisk
			highRiskCriteria []string
			lowRiskCriteria  []string
			priorityCriteria []string
		)

		precizeScore := riskScoreMap[riskLevel]

		if incidentDataMap.SourceRisk == common.NOTEVALUATED_RISK {
			incidentDataMap.SeverityDiff = 2
		} else if riskScoreMap[riskLevel] == riskScoreMap[incidentDataMap.SourceRisk] {
			incidentDataMap.SeverityDiff = 0
		} else if riskScoreMap[riskLevel] > riskScoreMap[incidentDataMap.SourceRisk] {
			incidentDataMap.SeverityDiff = 1
		} else {
			incidentDataMap.SeverityDiff = -1
		}

		for criteria, inc := range pData.PriorityCriteria {
			if incidentDataMap.SeverityDiff == 1 && inc {
				highRiskCriteria = append(highRiskCriteria, criteria)
			} else if incidentDataMap.SeverityDiff == -1 && !inc {
				lowRiskCriteria = append(lowRiskCriteria, criteria)
			} else if incidentDataMap.SeverityDiff == 0 || incidentDataMap.SeverityDiff == 2 {
				if inc {
					highRiskCriteria = append(highRiskCriteria, criteria)
				} else {
					lowRiskCriteria = append(lowRiskCriteria, criteria)
				}
			}
		}

		description := GeneratePrecizeCostIncidentRepriorDesc(highRiskCriteria, lowRiskCriteria, incidentDataMap)
		priorityCriteria = append(priorityCriteria, description)

		if _, ok := counts[incidentDataMap.SourceRisk]; !ok {
			counts[incidentDataMap.SourceRisk] = make(map[string]int)
		}

		counts[incidentDataMap.SourceRisk][riskLevel]++

		// Add escape characters if double quotes are present
		if len(incidentDataMap.AccountName) > 0 && (strings.Contains(incidentDataMap.AccountName, `"`) || strings.Contains(incidentDataMap.AccountName, `\`)) {
			incidentDataMap.AccountName = common.EscapeString(incidentDataMap.AccountName)
		}

		if len(incidentDataMap.ResourceName) > 0 && (strings.Contains(incidentDataMap.ResourceName, `"`) || strings.Contains(incidentDataMap.ResourceName, `\`)) {
			incidentDataMap.ResourceName = common.EscapeString(incidentDataMap.ResourceName)
		}

		incidentUpdateMetadata = `{"update": {"_id": "` + incidentDocId + `"}}`
		incidentUpdateDoc = `{"doc": {"precizeRisk": "` + riskLevel +
			`", "entityId":"` + incidentDataMap.EntityID +
			`", "entityType":"` + incidentDataMap.EntityType +
			`", "crsId":"` + incidentDataMap.CrsID +
			`", "owner":["` + strings.Join(incidentDataMap.Owner, "\",\"") +
			`"], "environment":["` + strings.Join(incidentDataMap.Environment, "\",\"") +
			`"], "resourceName":"` + incidentDataMap.ResourceName +
			`", "accountId":"` + incidentDataMap.AccountID +
			`", "accountName":"` + incidentDataMap.AccountName +
			`", "severityDiff":` + strconv.Itoa(incidentDataMap.SeverityDiff) +
			`, "precizeScore":` + fmt.Sprintf("%.2f", precizeScore) +
			`,"stage": "prioritised", "priorityCriteria":["` + strings.Join(priorityCriteria, "\",\"") +
			`"]}}`
	} else if len(incidentDataMap.CrsID) > 0 {
		// Resource not found
		incidentUpdateMetadata = `{"update": {"_id": "` + incidentDocId + `"}}`
		incidentUpdateDoc = `{"doc": {"stage": "notfound"}}`
	} else {
		// EntityType mapping not found
		return nil
	}

	*bulkUpdateQuery = *bulkUpdateQuery + incidentUpdateMetadata + "\n" + incidentUpdateDoc + "\n"
	*currentCount++

	return
}
